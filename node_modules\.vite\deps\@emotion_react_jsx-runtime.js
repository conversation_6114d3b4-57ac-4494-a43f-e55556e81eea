import {
  require_jsx_runtime
} from "./chunk-NJLIVH7H.js";
import {
  Emotion$1,
  createEmotionProps,
  hasOwn,
  init_emotion_cache_browser_development_esm,
  init_emotion_element_489459f2_browser_development_esm,
  init_emotion_react_isolated_hnrs_browser_development_esm,
  init_emotion_serialize_development_esm,
  init_emotion_use_insertion_effect_with_fallbacks_browser_esm,
  init_emotion_utils_browser_esm,
  init_emotion_weak_memoize_esm,
  require_hoist_non_react_statics_cjs
} from "./chunk-WSUKRXHS.js";
import {
  init_extends
} from "./chunk-Q7CPF5VB.js";
import {
  require_react
} from "./chunk-HUL2CLQT.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@emotion/react/jsx-runtime/dist/emotion-react-jsx-runtime.browser.development.esm.js
var ReactJSXRuntime = __toESM(require_jsx_runtime());
init_emotion_element_489459f2_browser_development_esm();
var import_react = __toESM(require_react());
init_emotion_cache_browser_development_esm();
init_extends();
init_emotion_weak_memoize_esm();
init_emotion_react_isolated_hnrs_browser_development_esm();
var import_hoist_non_react_statics = __toESM(require_hoist_non_react_statics_cjs());
init_emotion_utils_browser_esm();
init_emotion_serialize_development_esm();
init_emotion_use_insertion_effect_with_fallbacks_browser_esm();
var Fragment2 = ReactJSXRuntime.Fragment;
var jsx2 = function jsx3(type, props, key) {
  if (!hasOwn.call(props, "css")) {
    return ReactJSXRuntime.jsx(type, props, key);
  }
  return ReactJSXRuntime.jsx(Emotion$1, createEmotionProps(type, props), key);
};
var jsxs2 = function jsxs3(type, props, key) {
  if (!hasOwn.call(props, "css")) {
    return ReactJSXRuntime.jsxs(type, props, key);
  }
  return ReactJSXRuntime.jsxs(Emotion$1, createEmotionProps(type, props), key);
};
export {
  Fragment2 as Fragment,
  jsx2 as jsx,
  jsxs2 as jsxs
};
//# sourceMappingURL=@emotion_react_jsx-runtime.js.map
