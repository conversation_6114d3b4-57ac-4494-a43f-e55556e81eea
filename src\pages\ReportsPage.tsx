import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  But<PERSON>,
  <PERSON>,
} from '@mui/material';
import {
  Assessment,
  TrendingUp,
  AttachMoney,
  People,
  Download,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';

const ReportsPage: React.FC = () => {
  const salesData = [
    { name: 'Jan', sales: 4000, orders: 240 },
    { name: 'Feb', sales: 3000, orders: 198 },
    { name: 'Mar', sales: 5000, orders: 300 },
    { name: 'Apr', sales: 4500, orders: 280 },
    { name: 'May', sales: 6000, orders: 350 },
    { name: 'Jun', sales: 5500, orders: 320 },
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
            color: '#FFFF00',
            textShadow: '2px 2px 0px #000000',
            marginBottom: '24px'
          }}>
            REPORTS & ANALYTICS
          </Typography>
          <Typography variant="body1" sx={{ color: '#FFFFFF', fontSize: '1.1rem' }}>
            Comprehensive business insights and performance metrics
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Download />} size="large">
          Export Reports
        </Button>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FF00' }}>
                    $24,500
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Monthly Revenue
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: '#00FF00' }} />
              </Box>
              <Chip label="+12% vs last month" sx={{
                mt: 1,
                backgroundColor: '#00FF00',
                color: '#000000',
                border: '2px solid #000000',
                fontWeight: 'bold'
              }} size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FFFF',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FFFF',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FFFF',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FFFF' }}>
                    1,247
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Total Orders
                  </Typography>
                </Box>
                <Assessment sx={{ fontSize: 40, color: '#00FFFF' }} />
              </Box>
              <Chip label="+8% vs last month" sx={{
                mt: 1,
                backgroundColor: '#00FF00',
                color: '#000000',
                border: '2px solid #000000',
                fontWeight: 'bold'
              }} size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FF00FF',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FF00FF',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FF00FF',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FF00FF' }}>
                    892
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Customers Served
                  </Typography>
                </Box>
                <People sx={{ fontSize: 40, color: '#FF00FF' }} />
              </Box>
              <Chip label="+15% vs last month" sx={{
                mt: 1,
                backgroundColor: '#00FF00',
                color: '#000000',
                border: '2px solid #000000',
                fontWeight: 'bold'
              }} size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFF00' }}>
                    $19.65
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Avg Order Value
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, color: '#FFFF00' }} />
              </Box>
              <Chip label="+3% vs last month" sx={{
                mt: 1,
                backgroundColor: '#00FF00',
                color: '#000000',
                border: '2px solid #000000',
                fontWeight: 'bold'
              }} size="small" />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
          }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                Sales Trend (Last 6 Months)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#333333" />
                  <XAxis dataKey="name" tick={{ fill: '#FFFFFF' }} />
                  <YAxis tick={{ fill: '#FFFFFF' }} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#000000',
                      border: '2px solid #FFFF00',
                      color: '#FFFFFF'
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="sales"
                    stroke="#FFFF00"
                    strokeWidth={4}
                    dot={{ fill: '#FFFF00', strokeWidth: 2, r: 8, stroke: '#000000' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
          }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                Order Volume
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#333333" />
                  <XAxis dataKey="name" tick={{ fill: '#FFFFFF' }} />
                  <YAxis tick={{ fill: '#FFFFFF' }} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#000000',
                      border: '2px solid #FFFF00',
                      color: '#FFFFFF'
                    }}
                  />
                  <Bar dataKey="orders" fill="#FFFF00" stroke="#000000" strokeWidth={2} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReportsPage;
