import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
} from '@mui/material';
import {
  Settings,
  Store,
  Notifications,
  Security,
  Payment,
  Print,
  Wifi,
} from '@mui/icons-material';

const SettingsPage: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
        color: '#FFFF00',
        textShadow: '2px 2px 0px #000000',
        marginBottom: '24px'
      }}>
        SETTINGS
      </Typography>
      <Typography variant="body1" sx={{ color: '#FFFFFF', fontSize: '1.1rem', mb: 4 }}>
        Configure your restaurant system preferences
      </Typography>

      <Grid container spacing={3}>
        {/* Restaurant Information */}
        <Grid item xs={12} md={6}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: '#FFFF00', color: '#000000', mr: 2 }}>
                  <Store />
                </Avatar>
                <Typography variant="h6" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                  Restaurant Information
                </Typography>
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Restaurant Name"
                    defaultValue="The Golden Spoon"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Address"
                    defaultValue="123 Main Street, City, State 12345"
                    variant="outlined"
                    multiline
                    rows={2}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Phone"
                    defaultValue="+****************"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    defaultValue="<EMAIL>"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button variant="contained" sx={{
                    mt: 2,
                    backgroundColor: '#FFFF00',
                    color: '#000000',
                    border: '2px solid #000000',
                    borderRadius: 0,
                    fontWeight: 'bold',
                    textTransform: 'uppercase',
                    boxShadow: '3px 3px 0px #000000',
                    '&:hover': {
                      backgroundColor: '#FFFF00',
                      transform: 'translate(-1px, -1px)',
                      boxShadow: '4px 4px 0px #000000',
                    }
                  }}>
                    SAVE CHANGES
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* System Preferences */}
        <Grid item xs={12} md={6}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: '#FFFF00', color: '#000000', mr: 2 }}>
                  <Settings />
                </Avatar>
                <Typography variant="h6" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                  System Preferences
                </Typography>
              </Box>
              
              <List>
                <ListItem sx={{ color: '#FFFFFF' }}>
                  <ListItemText
                    primary="Auto-print receipts"
                    secondary="Automatically print receipts after payment"
                    sx={{
                      '& .MuiListItemText-primary': { color: '#FFFFFF', fontWeight: 'bold' },
                      '& .MuiListItemText-secondary': { color: '#CCCCCC' }
                    }}
                  />
                  <ListItemSecondaryAction>
                    <Switch defaultChecked sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem sx={{ color: '#FFFFFF' }}>
                  <ListItemText
                    primary="Sound notifications"
                    secondary="Play sounds for new orders and alerts"
                    sx={{
                      '& .MuiListItemText-primary': { color: '#FFFFFF', fontWeight: 'bold' },
                      '& .MuiListItemText-secondary': { color: '#CCCCCC' }
                    }}
                  />
                  <ListItemSecondaryAction>
                    <Switch defaultChecked sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem sx={{ color: '#FFFFFF' }}>
                  <ListItemText
                    primary="24-hour time format"
                    secondary="Display time in 24-hour format"
                    sx={{
                      '& .MuiListItemText-primary': { color: '#FFFFFF', fontWeight: 'bold' },
                      '& .MuiListItemText-secondary': { color: '#CCCCCC' }
                    }}
                  />
                  <ListItemSecondaryAction>
                    <Switch sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem sx={{ color: '#FFFFFF' }}>
                  <ListItemText
                    primary="Auto-backup data"
                    secondary="Automatically backup data daily"
                    sx={{
                      '& .MuiListItemText-primary': { color: '#FFFFFF', fontWeight: 'bold' },
                      '& .MuiListItemText-secondary': { color: '#CCCCCC' }
                    }}
                  />
                  <ListItemSecondaryAction>
                    <Switch defaultChecked sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Payment Settings */}
        <Grid item xs={12} md={6}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: '#00FF00', color: '#000000', mr: 2 }}>
                  <Payment />
                </Avatar>
                <Typography variant="h6" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                  Payment Settings
                </Typography>
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Tax Rate (%)"
                    defaultValue="8.5"
                    variant="outlined"
                    type="number"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Service Charge (%)"
                    defaultValue="10"
                    variant="outlined"
                    type="number"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={<Switch defaultChecked sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />}
                    label="Accept Credit Cards"
                    sx={{ color: '#FFFFFF', fontWeight: 'bold' }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={<Switch defaultChecked sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />}
                    label="Accept Digital Payments"
                    sx={{ color: '#FFFFFF', fontWeight: 'bold' }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button variant="contained" sx={{
                    mt: 2,
                    backgroundColor: '#FFFF00',
                    color: '#000000',
                    border: '2px solid #000000',
                    borderRadius: 0,
                    fontWeight: 'bold',
                    textTransform: 'uppercase',
                    boxShadow: '3px 3px 0px #000000',
                    '&:hover': {
                      backgroundColor: '#FFFF00',
                      transform: 'translate(-1px, -1px)',
                      boxShadow: '4px 4px 0px #000000',
                    }
                  }}>
                    UPDATE PAYMENT SETTINGS
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Printer Settings */}
        <Grid item xs={12} md={6}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: '#00FFFF', color: '#000000', mr: 2 }}>
                  <Print />
                </Avatar>
                <Typography variant="h6" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                  Printer Settings
                </Typography>
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Receipt Printer"
                    defaultValue="HP LaserJet Pro"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Kitchen Printer"
                    defaultValue="Epson TM-T88V"
                    variant="outlined"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={<Switch defaultChecked sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />}
                    label="Print Kitchen Orders"
                    sx={{ color: '#FFFFFF', fontWeight: 'bold' }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={<Switch sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />}
                    label="Print Customer Copy"
                    sx={{ color: '#FFFFFF', fontWeight: 'bold' }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button variant="outlined" sx={{
                    mr: 2,
                    color: '#FFFF00',
                    border: '2px solid #FFFF00',
                    borderRadius: 0,
                    fontWeight: 'bold',
                    textTransform: 'uppercase',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 0, 0.1)',
                      border: '2px solid #FFFF00',
                    }
                  }}>
                    TEST PRINT
                  </Button>
                  <Button variant="contained" sx={{
                    backgroundColor: '#FFFF00',
                    color: '#000000',
                    border: '2px solid #000000',
                    borderRadius: 0,
                    fontWeight: 'bold',
                    textTransform: 'uppercase',
                    boxShadow: '3px 3px 0px #000000',
                    '&:hover': {
                      backgroundColor: '#FFFF00',
                      transform: 'translate(-1px, -1px)',
                      boxShadow: '4px 4px 0px #000000',
                    }
                  }}>
                    SAVE SETTINGS
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Settings */}
        <Grid item xs={12}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar sx={{ bgcolor: '#FF0000', color: '#FFFFFF', mr: 2 }}>
                  <Security />
                </Avatar>
                <Typography variant="h6" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                  Security & Access
                </Typography>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: '#FFFFFF', fontWeight: 'bold' }}>
                    Session Timeout
                  </Typography>
                  <TextField
                    fullWidth
                    label="Minutes"
                    defaultValue="30"
                    variant="outlined"
                    type="number"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: '#000000',
                        border: '2px solid #FFFF00',
                        borderRadius: 0,
                        color: '#FFFFFF',
                        '& fieldset': { border: 'none' },
                        '&:hover fieldset': { border: 'none' },
                        '&.Mui-focused fieldset': { border: 'none' }
                      },
                      '& .MuiInputLabel-root': { color: '#FFFF00' },
                      '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
                    }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: '#FFFFFF', fontWeight: 'bold' }}>
                    Password Requirements
                  </Typography>
                  <FormControlLabel
                    control={<Switch defaultChecked sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />}
                    label="Require strong passwords"
                    sx={{ color: '#FFFFFF', fontWeight: 'bold' }}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle2" gutterBottom sx={{ color: '#FFFFFF', fontWeight: 'bold' }}>
                    Two-Factor Authentication
                  </Typography>
                  <FormControlLabel
                    control={<Switch sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': { color: '#FFFF00' },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': { backgroundColor: '#FFFF00' }
                    }} />}
                    label="Enable 2FA"
                    sx={{ color: '#FFFFFF', fontWeight: 'bold' }}
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 3, backgroundColor: '#FFFF00' }} />

              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button variant="outlined" sx={{
                  color: '#FF0000',
                  border: '2px solid #FF0000',
                  borderRadius: 0,
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 0, 0, 0.1)',
                    border: '2px solid #FF0000',
                  }
                }}>
                  RESET ALL SETTINGS
                </Button>
                <Button variant="contained" sx={{
                  backgroundColor: '#FFFF00',
                  color: '#000000',
                  border: '2px solid #000000',
                  borderRadius: 0,
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  boxShadow: '3px 3px 0px #000000',
                  '&:hover': {
                    backgroundColor: '#FFFF00',
                    transform: 'translate(-1px, -1px)',
                    boxShadow: '4px 4px 0px #000000',
                  }
                }}>
                  SAVE SECURITY SETTINGS
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SettingsPage;
