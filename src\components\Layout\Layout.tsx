import React, { useState } from 'react';
import {
  <PERSON>,
  Drawer,
  App<PERSON><PERSON>,
  Too<PERSON><PERSON>,
  <PERSON><PERSON>graphy,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications,
  AccountCircle,
  Logout,
  Settings,
  Restaurant,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useAuth } from '../../contexts/AuthContext';
import Sidebar from './Sidebar';

const drawerWidth = 280;

const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })<{
  open?: boolean;
}>(({ theme, open }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: `-${drawerWidth}px`,
  width: '100%',
  maxWidth: '100%',
  height: '100vh',
  overflowY: 'auto',
  backgroundColor: theme.palette.background.default,
  '&::-webkit-scrollbar': {
    width: '12px',
  },
  '&::-webkit-scrollbar-track': {
    background: '#FFFFFF',
    border: '2px solid #000000',
    borderRadius: 0,
  },
  '&::-webkit-scrollbar-thumb': {
    background: '#FFFF00',
    border: '2px solid #000000',
    borderRadius: 0,
    boxShadow: '2px 2px 0px #000000',
  },
  '&::-webkit-scrollbar-thumb:hover': {
    background: '#FFFF66',
    boxShadow: '3px 3px 0px #000000',
  },
  ...(open && {
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
    marginLeft: 0,
    width: `calc(100% - ${drawerWidth}px)`,
  }),
}));

const StyledAppBar = styled(AppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open?: boolean }>(({ theme, open }) => ({
  backgroundColor: '#000000', // Black background
  color: '#FFFF00', // Yellow text
  border: '3px solid #FFFF00',
  borderTop: 'none',
  borderLeft: 'none',
  borderRight: 'none',
  boxShadow: '0 4px 0px #FFFF00',
  transition: theme.transitions.create(['margin', 'width'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  '& .MuiToolbar-root': {
    backgroundColor: '#000000',
    color: '#FFFF00',
  },
  '& .MuiIconButton-root': {
    color: '#FFFF00',
    '&:hover': {
      backgroundColor: 'rgba(255, 255, 0, 0.1)',
    },
  },
  '& .MuiBadge-badge': {
    backgroundColor: '#FF0000',
    color: '#FFFFFF',
  },
  ...(open && {
    width: `calc(100% - ${drawerWidth}px)`,
    marginLeft: `${drawerWidth}px`,
    transition: theme.transitions.create(['margin', 'width'], {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(0, 1),
  ...theme.mixins.toolbar,
  justifyContent: 'flex-end',
}));

// Custom Fork and Spoon SVG Components
const ForkIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M7 2v7.5c0 1.5 1 2.5 2.5 2.5S12 11 12 9.5V2h-1v7.5c0 1-0.5 1.5-1.5 1.5S8 10.5 8 9.5V2H7zm5 0v7.5c0 1.5 1 2.5 2.5 2.5S17 11 17 9.5V2h-1v7.5c0 1-0.5 1.5-1.5 1.5S13 10.5 13 9.5V2h-1zm-3 10v10h2V12H9z"/>
  </svg>
);

const SpoonIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12.5 2C10.5 2 9 3.5 9 5.5c0 1.5 1 2.8 2.4 3.2L11 22h3l-0.4-13.3C15 8.3 16 7 16 5.5C16 3.5 14.5 2 12.5 2z"/>
  </svg>
);

// Animated components
const AnimatedROS = styled(Typography)(({ theme }) => ({
  fontWeight: 800,
  fontSize: '1.5rem',
  color: '#FFFF00',
  textShadow: '2px 2px 0px #000000',
  animation: 'glow 2s ease-in-out infinite alternate',
  '@keyframes glow': {
    '0%': {
      transform: 'scale(1)',
      textShadow: '2px 2px 0px #000000, 0 0 10px #FFFF00',
    },
    '100%': {
      transform: 'scale(1.05)',
      textShadow: '3px 3px 0px #000000, 0 0 20px #FFFF00, 0 0 30px #FFFF00',
    },
  },
}));

const AnimatedFork = styled(ForkIcon)(({ theme }) => ({
  fontSize: '1.5rem',
  color: '#FFFF00',
  marginRight: theme.spacing(0.5),
  animation: 'bounce 2s ease-in-out infinite',
  '@keyframes bounce': {
    '0%, 100%': {
      transform: 'translateY(0) rotate(-10deg)',
    },
    '50%': {
      transform: 'translateY(-5px) rotate(-15deg)',
    },
  },
}));

const AnimatedSpoon = styled(SpoonIcon)(({ theme }) => ({
  fontSize: '1.5rem',
  color: '#FFFF00',
  marginLeft: theme.spacing(0.5),
  animation: 'swing 2s ease-in-out infinite',
  animationDelay: '0.5s',
  '@keyframes swing': {
    '0%, 100%': {
      transform: 'translateY(0) rotate(10deg)',
    },
    '50%': {
      transform: 'translateY(-5px) rotate(15deg)',
    },
  },
}));

const AnimatedRestaurant = styled(Restaurant)(({ theme }) => ({
  fontSize: '2rem',
  color: '#FFFF00',
  marginRight: theme.spacing(1),
  animation: 'pulse 3s ease-in-out infinite',
  '@keyframes pulse': {
    '0%, 100%': {
      transform: 'scale(1)',
      filter: 'drop-shadow(0 0 5px #FFFF00)',
    },
    '50%': {
      transform: 'scale(1.1)',
      filter: 'drop-shadow(0 0 15px #FFFF00)',
    },
  },
}));

const AnimatedSubtext = styled(Typography)(({ theme }) => ({
  color: '#FFFF00',
  fontSize: '0.9rem',
  opacity: 0.9,
  animation: 'fadeInOut 4s ease-in-out infinite',
  '@keyframes fadeInOut': {
    '0%, 100%': {
      opacity: 0.7,
    },
    '50%': {
      opacity: 1,
    },
  },
}));

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [open, setOpen] = useState(true);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { user, logout } = useAuth();

  const handleDrawerToggle = () => {
    setOpen(!open);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleProfileMenuClose();
    logout();
  };

  return (
    <Box sx={{ display: 'flex', width: '100%', minHeight: '100vh' }}>
      <StyledAppBar position="fixed" open={open}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerToggle}
            edge="start"
            sx={{
              mr: 2,
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 0, 0.1)',
                transform: 'scale(1.1)',
                transition: 'all 0.3s ease',
              },
            }}
          >
            <MenuIcon />
          </IconButton>
          
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <AnimatedSubtext variant="h6" sx={{ fontSize: '1.2rem', fontWeight: 600, ml: 1 }}>
              Restaurant Operating System
            </AnimatedSubtext>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Notifications">
              <IconButton color="inherit">
                <Badge
                  badgeContent={3}
                  sx={{
                    '& .MuiBadge-badge': {
                      backgroundColor: '#FF0000',
                      color: '#FFFFFF',
                      border: '3px solid #FFFF00',
                      fontWeight: 'bold',
                      borderRadius: '50%',
                      padding: '4px 6px',
                      minWidth: '20px',
                      height: '20px',
                      animation: 'pulse 2s ease-in-out infinite',
                      '@keyframes pulse': {
                        '0%, 100%': {
                          transform: 'scale(1)',
                          boxShadow: '0 0 0 0 rgba(255, 0, 0, 0.7)',
                        },
                        '50%': {
                          transform: 'scale(1.1)',
                          boxShadow: '0 0 0 10px rgba(255, 0, 0, 0)',
                        },
                      },
                    }
                  }}
                >
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Account settings">
              <IconButton
                onClick={handleProfileMenuOpen}
                color="inherit"
                sx={{ ml: 1 }}
              >
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: '#FFFF00',
                    color: '#000000',
                    border: '2px solid #FFFF00',
                    fontWeight: 'bold'
                  }}
                >
                  {user?.firstName?.charAt(0) || 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </StyledAppBar>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        PaperProps={{
          elevation: 0,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,
            '& .MuiAvatar-root': {
              width: 32,
              height: 32,
              ml: -0.5,
              mr: 1,
            },
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleProfileMenuClose}>
          <Avatar sx={{
            bgcolor: '#FFFF00',
            color: '#000000',
            border: '2px solid #FFFF00',
            fontWeight: 'bold'
          }}>
            {user?.firstName?.charAt(0) || 'U'}
          </Avatar>
          <Box>
            <Typography variant="subtitle2">
              {user?.firstName} {user?.lastName}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {user?.role}
            </Typography>
          </Box>
        </MenuItem>
        
        <MenuItem onClick={handleProfileMenuClose}>
          <Settings fontSize="small" sx={{ mr: 2 }} />
          Settings
        </MenuItem>
        
        <MenuItem onClick={handleLogout}>
          <Logout fontSize="small" sx={{ mr: 2 }} />
          Logout
        </MenuItem>
      </Menu>

      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
        variant="persistent"
        anchor="left"
        open={open}
      >
        <Sidebar />
      </Drawer>

      <Main open={open}>
        <DrawerHeader />
        {children}
      </Main>
    </Box>
  );
};

export default Layout;
