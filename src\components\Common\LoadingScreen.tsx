import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  height: '100vh',
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  textAlign: 'center',
}));

const LoadingScreen: React.FC = () => {
  return (
    <LoadingContainer>
      <LogoContainer>
        <Typography variant="h2" component="h1" gutterBottom className="text-gradient">
          ROS
        </Typography>
        <Typography variant="h6" component="p" sx={{ opacity: 0.9 }}>
          Restaurant Operating System
        </Typography>
      </LogoContainer>
      
      <CircularProgress 
        size={60} 
        thickness={4}
        sx={{ 
          color: 'white',
          marginBottom: 2
        }} 
      />
      
      <Typography variant="body1" sx={{ opacity: 0.8 }}>
        Loading your restaurant...
      </Typography>
    </LoadingContainer>
  );
};

export default LoadingScreen;
