import React from 'react';

const TestApp: React.FC = () => {
  return (
    <div style={{
      padding: '50px',
      textAlign: 'center',
      background: 'linear-gradient(135deg, #2C1810 0%, #4A2C1A 100%)',
      color: 'white',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: '#D4AF37', fontSize: '3em', marginBottom: '20px' }}>
        🏪 ROS Test
      </h1>
      <h2>Restaurant Operating System</h2>
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        padding: '20px',
        borderRadius: '10px',
        margin: '20px auto',
        maxWidth: '500px'
      }}>
        <p>✅ React is working!</p>
        <p>✅ TypeScript is working!</p>
        <p>✅ Vite dev server is working!</p>
      </div>
      <p>If you can see this, the basic setup is functioning correctly.</p>
    </div>
  );
};

export default TestApp;
