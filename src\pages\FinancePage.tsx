import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  AccountBalance,
  TrendingUp,
  TrendingDown,
  AttachMoney,
  Receipt,
  Download,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

const FinancePage: React.FC = () => {
  const profitData = [
    { name: 'Jan', revenue: 24000, expenses: 18000, profit: 6000 },
    { name: 'Feb', revenue: 22000, expenses: 17000, profit: 5000 },
    { name: 'Mar', revenue: 28000, expenses: 19000, profit: 9000 },
    { name: 'Apr', revenue: 26000, expenses: 18500, profit: 7500 },
    { name: 'May', revenue: 30000, expenses: 20000, profit: 10000 },
    { name: 'Jun', revenue: 32000, expenses: 21000, profit: 11000 },
  ];

  const transactions = [
    { id: '1', type: 'Revenue', description: 'Daily Sales', amount: 2450, date: '2024-01-20' },
    { id: '2', type: 'Expense', description: 'Food Supplies', amount: -850, date: '2024-01-20' },
    { id: '3', type: 'Expense', description: 'Staff Salaries', amount: -3200, date: '2024-01-19' },
    { id: '4', type: 'Revenue', description: 'Catering Order', amount: 1200, date: '2024-01-19' },
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
            color: '#FFFF00',
            textShadow: '2px 2px 0px #000000',
            marginBottom: '24px'
          }}>
            FINANCE & ACCOUNTING
          </Typography>
          <Typography variant="body1" sx={{ color: '#FFFFFF', fontSize: '1.1rem' }}>
            Financial overview and accounting management
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Download />} size="large">
          Export Reports
        </Button>
      </Box>

      {/* Financial Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FF00' }}>
                    $32,000
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Monthly Revenue
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, color: '#00FF00' }} />
              </Box>
              <Chip label="+12% vs last month" sx={{
                mt: 1,
                backgroundColor: '#00FF00',
                color: '#000000',
                border: '2px solid #000000',
                fontWeight: 'bold'
              }} size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FF0000',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FF0000',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FF0000',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FF0000' }}>
                    $21,000
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Monthly Expenses
                  </Typography>
                </Box>
                <TrendingDown sx={{ fontSize: 40, color: '#FF0000' }} />
              </Box>
              <Chip label="+5% vs last month" sx={{
                mt: 1,
                backgroundColor: '#FF0000',
                color: '#FFFFFF',
                border: '2px solid #000000',
                fontWeight: 'bold'
              }} size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFF00' }}>
                    $11,000
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Net Profit
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: '#FFFF00' }} />
              </Box>
              <Chip label="+25% vs last month" sx={{
                mt: 1,
                backgroundColor: '#00FF00',
                color: '#000000',
                border: '2px solid #000000',
                fontWeight: 'bold'
              }} size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FFFF',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FFFF',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FFFF',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FFFF' }}>
                    34.4%
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Profit Margin
                  </Typography>
                </Box>
                <AccountBalance sx={{ fontSize: 40, color: '#00FFFF' }} />
              </Box>
              <Chip label="+3% vs last month" sx={{
                mt: 1,
                backgroundColor: '#00FF00',
                color: '#000000',
                border: '2px solid #000000',
                fontWeight: 'bold'
              }} size="small" />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts and Transactions */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
          }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                Profit & Loss Trend
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={profitData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#333333" />
                  <XAxis dataKey="name" tick={{ fill: '#FFFFFF' }} />
                  <YAxis tick={{ fill: '#FFFFFF' }} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#000000',
                      border: '2px solid #FFFF00',
                      color: '#FFFFFF'
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#00FF00"
                    strokeWidth={3}
                    name="Revenue"
                    dot={{ fill: '#00FF00', strokeWidth: 2, r: 6, stroke: '#000000' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="expenses"
                    stroke="#FF0000"
                    strokeWidth={3}
                    name="Expenses"
                    dot={{ fill: '#FF0000', strokeWidth: 2, r: 6, stroke: '#000000' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="profit"
                    stroke="#FFFF00"
                    strokeWidth={4}
                    name="Profit"
                    dot={{ fill: '#FFFF00', strokeWidth: 2, r: 8, stroke: '#000000' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
          }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                Recent Transactions
              </Typography>
              <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                {transactions.map((transaction) => (
                  <Box
                    key={transaction.id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      py: 1,
                      borderBottom: '1px solid',
                      borderColor: 'divider',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Receipt sx={{ mr: 1, color: 'text.secondary' }} />
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {transaction.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {transaction.date}
                        </Typography>
                      </Box>
                    </Box>
                    <Typography
                      variant="body2"
                      fontWeight="bold"
                      color={transaction.amount > 0 ? 'success.main' : 'error.main'}
                    >
                      {transaction.amount > 0 ? '+' : ''}${Math.abs(transaction.amount)}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FinancePage;
