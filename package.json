{"name": "ros", "private": true, "version": "1.0.0", "description": "360° Restaurant Operating System", "main": "electron/main.js", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "electron-pack": "npm run build && electron-builder", "preelectron-pack": "npm run build"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.2", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.1.2", "@mui/x-data-grid": "^8.5.3", "@mui/x-date-pickers": "^8.5.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "electron": "^36.5.0", "electron-builder": "^26.0.12", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-router-dom": "^7.6.2", "recharts": "^2.15.4", "uuid": "^11.1.0", "wait-on": "^8.0.3", "yup": "^1.6.1"}}