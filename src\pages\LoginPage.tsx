import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Divider,
  Chip,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Restaurant,
  Person,
  Lock,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useAuth } from '../contexts/AuthContext';

const LoginContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
}));

const LoginCard = styled(Card)(({ theme }) => ({
  width: '100%',
  maxWidth: 450,
  margin: 'auto',
  marginTop: theme.spacing(8),
  marginBottom: theme.spacing(8),
  borderRadius: 24,
  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(10px)',
}));

const LogoSection = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(4, 4, 2, 4),
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  borderRadius: '24px 24px 0 0',
}));

const DemoCredentials = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.default,
  borderRadius: 8,
  border: `1px solid ${theme.palette.divider}`,
}));

const LoginPage: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const success = await login(username, password);
      if (!success) {
        setError('Invalid username or password');
      }
    } catch (err) {
      setError('An error occurred during login');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = (role: string) => {
    setUsername(role);
    setPassword('password');
  };

  return (
    <LoginContainer>
      <LoginCard>
        <LogoSection>
          <Restaurant sx={{ fontSize: 48, marginBottom: 1 }} />
          <Typography variant="h4" component="h1" fontWeight="bold">
            ROS
          </Typography>
          <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
            Restaurant Operating System
          </Typography>
        </LogoSection>

        <CardContent sx={{ padding: 4 }}>
          <Typography variant="h5" component="h2" textAlign="center" gutterBottom>
            Welcome Back
          </Typography>
          <Typography variant="body2" textAlign="center" color="text.secondary" sx={{ mb: 3 }}>
            Sign in to manage your restaurant
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              margin="normal"
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person color="action" />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              fullWidth
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              margin="normal"
              required
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isLoading}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
          </Box>

          <Divider sx={{ my: 3 }}>
            <Typography variant="body2" color="text.secondary">
              Demo Accounts
            </Typography>
          </Divider>

          <DemoCredentials>
            <Typography variant="subtitle2" gutterBottom>
              Quick Login (Password: password)
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              <Chip
                label="Admin"
                onClick={() => handleDemoLogin('admin')}
                color="primary"
                variant="outlined"
                size="small"
              />
              <Chip
                label="Manager"
                onClick={() => handleDemoLogin('manager')}
                color="secondary"
                variant="outlined"
                size="small"
              />
              <Chip
                label="Cashier"
                onClick={() => handleDemoLogin('cashier')}
                color="info"
                variant="outlined"
                size="small"
              />
            </Box>
          </DemoCredentials>
        </CardContent>
      </LoginCard>
    </LoginContainer>
  );
};

export default LoginPage;
