import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Divider,
  Chip,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Restaurant,
  Person,
  Lock,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useAuth } from '../contexts/AuthContext';

const LoginContainer = styled(Box)(() => ({
  display: 'flex',
  minHeight: '100vh',
  backgroundColor: '#000000',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '20px',
}));

const LoginCard = styled(Card)(() => ({
  width: '100%',
  maxWidth: 450,
  backgroundColor: '#000000',
  border: '4px solid #FFFF00',
  borderRadius: 0,
  boxShadow: '8px 8px 0px #FFFF00',
  position: 'relative',
}));

const LogoSection = styled(Box)(() => ({
  textAlign: 'center',
  padding: '32px 32px 24px 32px',
  backgroundColor: '#000000',
  color: '#FFFF00',
  borderBottom: '3px solid #FFFF00',
}));

const DemoCredentials = styled(Box)(() => ({
  marginTop: '16px',
  padding: '16px',
  backgroundColor: '#000000',
  border: '2px solid #FFFF00',
  borderRadius: 0,
}));

const LoginPage: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const success = await login(username, password);
      if (!success) {
        setError('Invalid username or password');
      }
    } catch (err) {
      setError('An error occurred during login');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = (role: string) => {
    setUsername(role);
    setPassword('password');
  };

  return (
    <LoginContainer>
      <LoginCard>
        <LogoSection>
          <Restaurant sx={{ fontSize: 64, marginBottom: 2, color: '#FFFF00' }} />
          <Typography variant="h3" component="h1" fontWeight="bold" sx={{
            color: '#FFFF00',
            textShadow: '3px 3px 0px #000000',
            marginBottom: 1
          }}>
            ROS
          </Typography>
          <Typography variant="h6" sx={{
            color: '#FFFFFF',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            letterSpacing: '2px'
          }}>
            Restaurant Operating System
          </Typography>
        </LogoSection>

        <CardContent sx={{ padding: 4, backgroundColor: '#000000' }}>
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom sx={{
            color: '#FFFFFF',
            fontWeight: 'bold',
            textTransform: 'uppercase',
            marginBottom: 2
          }}>
            Welcome Back
          </Typography>
          <Typography variant="body1" textAlign="center" sx={{
            color: '#CCCCCC',
            mb: 4,
            fontSize: '1.1rem'
          }}>
            Sign in to manage your restaurant
          </Typography>

          {error && (
            <Alert severity="error" sx={{
              mb: 2,
              backgroundColor: '#FF0000',
              color: '#FFFFFF',
              border: '2px solid #FFFF00',
              borderRadius: 0,
              fontWeight: 'bold',
              '& .MuiAlert-icon': {
                color: '#FFFF00'
              }
            }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              margin="normal"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#000000',
                  border: '3px solid #FFFF00',
                  borderRadius: 0,
                  color: '#FFFFFF',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  '& fieldset': { border: 'none' },
                  '&:hover fieldset': { border: 'none' },
                  '&.Mui-focused fieldset': { border: 'none' },
                  '&.Mui-focused': {
                    boxShadow: '4px 4px 0px #FFFF00',
                    transform: 'translate(-2px, -2px)'
                  }
                },
                '& .MuiInputLabel-root': {
                  color: '#FFFF00',
                  fontWeight: 'bold',
                  fontSize: '1rem'
                },
                '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person sx={{ color: '#FFFF00' }} />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              fullWidth
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              margin="normal"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: '#000000',
                  border: '3px solid #FFFF00',
                  borderRadius: 0,
                  color: '#FFFFFF',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  '& fieldset': { border: 'none' },
                  '&:hover fieldset': { border: 'none' },
                  '&.Mui-focused fieldset': { border: 'none' },
                  '&.Mui-focused': {
                    boxShadow: '4px 4px 0px #FFFF00',
                    transform: 'translate(-2px, -2px)'
                  }
                },
                '& .MuiInputLabel-root': {
                  color: '#FFFF00',
                  fontWeight: 'bold',
                  fontSize: '1rem'
                },
                '& .MuiInputLabel-root.Mui-focused': { color: '#FFFF00' }
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock sx={{ color: '#FFFF00' }} />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                      sx={{ color: '#FFFF00' }}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isLoading}
              sx={{
                mt: 4,
                mb: 2,
                py: 2,
                backgroundColor: '#FFFF00',
                color: '#000000',
                border: '3px solid #000000',
                borderRadius: 0,
                fontWeight: 'bold',
                fontSize: '1.2rem',
                textTransform: 'uppercase',
                boxShadow: '4px 4px 0px #000000',
                '&:hover': {
                  backgroundColor: '#FFFF00',
                  transform: 'translate(-2px, -2px)',
                  boxShadow: '6px 6px 0px #000000',
                },
                '&:disabled': {
                  backgroundColor: '#888888',
                  color: '#CCCCCC',
                  border: '3px solid #666666',
                  boxShadow: '4px 4px 0px #666666',
                }
              }}
            >
              {isLoading ? 'SIGNING IN...' : 'SIGN IN'}
            </Button>
          </Box>

          <Divider sx={{
            my: 4,
            backgroundColor: '#FFFF00',
            height: '2px',
            '&::before, &::after': {
              borderColor: '#FFFF00'
            }
          }}>
            <Typography variant="body1" sx={{
              color: '#FFFFFF',
              fontWeight: 'bold',
              textTransform: 'uppercase',
              backgroundColor: '#000000',
              padding: '0 16px'
            }}>
              Demo Accounts
            </Typography>
          </Divider>

          <DemoCredentials>
            <Typography variant="subtitle1" gutterBottom sx={{
              color: '#FFFFFF',
              fontWeight: 'bold',
              textAlign: 'center',
              marginBottom: 2
            }}>
              Quick Login (Password: password)
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, justifyContent: 'center' }}>
              <Button
                onClick={() => handleDemoLogin('manager')}
                variant="contained"
                sx={{
                  backgroundColor: '#FFFF00',
                  color: '#000000',
                  border: '2px solid #000000',
                  borderRadius: 0,
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  boxShadow: '3px 3px 0px #000000',
                  minWidth: '100px',
                  '&:hover': {
                    backgroundColor: '#FFFF00',
                    transform: 'translate(-1px, -1px)',
                    boxShadow: '4px 4px 0px #000000',
                  }
                }}
              >
                Manager
              </Button>
              <Button
                onClick={() => handleDemoLogin('cashier')}
                variant="outlined"
                sx={{
                  color: '#00FFFF',
                  border: '2px solid #00FFFF',
                  borderRadius: 0,
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  backgroundColor: '#000000',
                  minWidth: '100px',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 255, 255, 0.1)',
                    border: '2px solid #00FFFF',
                  }
                }}
              >
                Cashier
              </Button>
            </Box>
          </DemoCredentials>
        </CardContent>
      </LoginCard>
    </LoginContainer>
  );
};

export default LoginPage;
