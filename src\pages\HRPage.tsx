import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  People,
  PersonAdd,
  Schedule,
  AttachMoney,
  CheckCircle,
  Cancel,
} from '@mui/icons-material';

const HRPage: React.FC = () => {
  const employees = [
    {
      id: '1',
      name: 'John <PERSON>',
      role: 'Manager',
      email: '<EMAIL>',
      phone: '+1234567890',
      status: 'active',
      shift: 'Morning',
      salary: 4500,
    },
    {
      id: '2',
      name: '<PERSON>',
      role: 'Cashier',
      email: '<EMAIL>',
      phone: '+1234567891',
      status: 'active',
      shift: 'Evening',
      salary: 2800,
    },
    {
      id: '3',
      name: '<PERSON>',
      role: 'Chef',
      email: '<EMAIL>',
      phone: '+1234567892',
      status: 'active',
      shift: 'Full Day',
      salary: 3500,
    },
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Human Resources
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage employees, schedules, and payroll
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<PersonAdd />} size="large">
          Add Employee
        </Button>
      </Box>

      {/* HR Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" color="primary">
                    {employees.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Employees
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <People />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" color="success.main">
                    {employees.filter(e => e.status === 'active').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Today
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <CheckCircle />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" color="info.main">
                    8
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Hours Today
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <Schedule />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" color="warning.main">
                    $10,800
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Monthly Payroll
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Employee Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom fontWeight="bold">
            Employee Directory
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Employee</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Shift</TableCell>
                  <TableCell>Salary</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {employees.map((employee) => (
                  <TableRow key={employee.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                          {employee.name.charAt(0)}
                        </Avatar>
                        <Typography variant="subtitle2">{employee.name}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip label={employee.role} color="primary" variant="outlined" size="small" />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{employee.email}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {employee.phone}
                      </Typography>
                    </TableCell>
                    <TableCell>{employee.shift}</TableCell>
                    <TableCell>${employee.salary.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip
                        label={employee.status}
                        color={employee.status === 'active' ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Button size="small" variant="outlined">
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default HRPage;
