import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Avatar,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  People,
  PersonAdd,
  Schedule,
  AttachMoney,
  CheckCircle,
  Cancel,
} from '@mui/icons-material';

const HRPage: React.FC = () => {
  const employees = [
    {
      id: '1',
      name: 'John <PERSON>',
      role: 'Manager',
      email: '<EMAIL>',
      phone: '+1234567890',
      status: 'active',
      shift: 'Morning',
      salary: 4500,
    },
    {
      id: '2',
      name: '<PERSON>',
      role: 'Cashier',
      email: '<EMAIL>',
      phone: '+1234567891',
      status: 'active',
      shift: 'Evening',
      salary: 2800,
    },
    {
      id: '3',
      name: '<PERSON>',
      role: 'Chef',
      email: '<EMAIL>',
      phone: '+1234567892',
      status: 'active',
      shift: 'Full Day',
      salary: 3500,
    },
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
            color: '#FFFF00',
            textShadow: '2px 2px 0px #000000',
            marginBottom: '24px'
          }}>
            HUMAN RESOURCES
          </Typography>
          <Typography variant="body1" sx={{ color: '#FFFFFF', fontSize: '1.1rem' }}>
            Manage employees, schedules, and payroll
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<PersonAdd />} size="large">
          Add Employee
        </Button>
      </Box>

      {/* HR Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFF00' }}>
                    {employees.length}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Total Employees
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFFF00', color: '#000000' }}>
                  <People />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FF00' }}>
                    {employees.filter(e => e.status === 'active').length}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Active Today
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FF00', color: '#000000' }}>
                  <CheckCircle />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FFFF',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FFFF',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FFFF',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FFFF' }}>
                    8
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Hours Today
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FFFF', color: '#000000' }}>
                  <Schedule />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFA500',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFA500',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFA500',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFA500' }}>
                    $10,800
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Monthly Payroll
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFA500', color: '#000000' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Employee Table */}
      <Card sx={{
        border: '3px solid #FFFF00',
        backgroundColor: '#000000',
        borderRadius: 0,
        boxShadow: '4px 4px 0px #FFFF00',
      }}>
        <CardContent>
          <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
            Employee Directory
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Employee</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Shift</TableCell>
                  <TableCell>Salary</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {employees.map((employee) => (
                  <TableRow key={employee.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                          {employee.name.charAt(0)}
                        </Avatar>
                        <Typography variant="subtitle2">{employee.name}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip label={employee.role} color="primary" variant="outlined" size="small" />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{employee.email}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {employee.phone}
                      </Typography>
                    </TableCell>
                    <TableCell>{employee.shift}</TableCell>
                    <TableCell>${employee.salary.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip
                        label={employee.status}
                        color={employee.status === 'active' ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Button size="small" variant="outlined">
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default HRPage;
