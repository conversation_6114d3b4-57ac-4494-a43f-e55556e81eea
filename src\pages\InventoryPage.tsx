import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
} from '@mui/material';
import {
  Inventory,
  Warning,
  TrendingDown,
  Add,
  Edit,
} from '@mui/icons-material';
import { useData } from '../contexts/DataContext';

const InventoryPage: React.FC = () => {
  const { inventory } = useData();

  const getStockStatus = (current: number, min: number, max: number) => {
    const percentage = (current / max) * 100;
    if (percentage <= 20) return { status: 'critical', color: 'error' as const };
    if (percentage <= 40) return { status: 'low', color: 'warning' as const };
    return { status: 'good', color: 'success' as const };
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
            color: '#FFFF00',
            textShadow: '2px 2px 0px #000000',
            marginBottom: '24px'
          }}>
            INVENTORY MANAGEMENT
          </Typography>
          <Typography variant="body1" sx={{ color: '#FFFFFF', fontSize: '1.1rem' }}>
            Monitor stock levels and manage inventory items
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />} size="large">
          Add Item
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                    {inventory.length}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Total Items
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFFF00', color: '#000000' }}>
                  <Inventory />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FF0000',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FF0000',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FF0000',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FF0000' }}>
                    2
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Low Stock
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FF0000', color: '#FFFFFF' }}>
                  <Warning />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFA500',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFA500',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFA500',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFA500' }}>
                    1
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Critical Stock
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFA500', color: '#000000' }}>
                  <TrendingDown />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FF00' }}>
                    $12,450
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Total Value
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FF00', color: '#000000' }}>
                  <Inventory />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Inventory Table */}
      <Card sx={{
        border: '3px solid #FFFF00',
        backgroundColor: '#000000',
        borderRadius: 0,
        boxShadow: '4px 4px 0px #FFFF00',
      }}>
        <CardContent>
          <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
            Inventory Items
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Item</TableCell>
                  <TableCell>SKU</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Current Stock</TableCell>
                  <TableCell>Stock Level</TableCell>
                  <TableCell>Unit Price</TableCell>
                  <TableCell>Supplier</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {inventory.map((item) => {
                  const { status, color } = getStockStatus(item.currentStock, item.minStock, item.maxStock);
                  const percentage = (item.currentStock / item.maxStock) * 100;
                  
                  return (
                    <TableRow key={item.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ bgcolor: 'secondary.main', mr: 2, width: 32, height: 32 }}>
                            <Inventory fontSize="small" />
                          </Avatar>
                          <Typography variant="subtitle2">{item.name}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{item.sku}</TableCell>
                      <TableCell>{item.category}</TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {item.currentStock} {item.unit}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Min: {item.minStock} | Max: {item.maxStock}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ width: 100 }}>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min(percentage, 100)}
                            color={color}
                            sx={{ mb: 1 }}
                          />
                          <Chip
                            label={status}
                            color={color}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </TableCell>
                      <TableCell>${item.costPrice.toFixed(2)}</TableCell>
                      <TableCell>{item.supplier}</TableCell>
                      <TableCell>
                        <Button size="small" startIcon={<Edit />}>
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default InventoryPage;
