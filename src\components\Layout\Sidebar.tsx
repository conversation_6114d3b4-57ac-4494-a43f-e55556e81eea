import React, { useState, useEffect } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Chip,
} from '@mui/material';
import {
  Dashboard,
  PointOfSale,
  Restaurant,
  Inventory,
  ShoppingCart,
  Assessment,
  People,
  AccountBalance,
  Support,
  Settings,
  TrendingUp,
  LocalPizza,
  Fastfood,
  LocalDining,
  DeliveryDining,
  RoomService,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const SidebarHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3, 2),
  backgroundColor: '#000000', // Black background
  color: '#FFFF00', // Yellow text
  textAlign: 'center',
  border: '3px solid #FFFF00',
  borderLeft: 'none',
  borderRight: 'none',
  '& .MuiTypography-root': {
    color: '#FFFF00',
    fontWeight: 800,
    textShadow: '2px 2px 0px #000000',
  },
  '& .MuiSvgIcon-root': {
    color: '#FFFF00',
  },
}));

const SidebarContent = styled(Box)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: '#000000', // Black background (60%)
  color: '#FFFFFF', // White text for contrast
  '& .MuiTypography-root': {
    color: '#FFFFFF',
  },
  '& .MuiListItemIcon-root': {
    color: '#FFFF00', // Yellow icons
  },
  '& .MuiListItemText-primary': {
    color: '#FFFFFF',
  },
}));

// Delivery Story Animation Component
const DeliveryStoryAnimation: React.FC = () => {
  const [currentScene, setCurrentScene] = useState(0);

  const storyScenes = [
    {
      leftIcon: '📱',
      rightIcon: '👨‍🍳',
      description: 'Customer orders on phone',
      leftAnimation: 'phoneOrder',
      rightAnimation: 'cooking'
    },
    {
      leftIcon: '⏰',
      rightIcon: '🍕',
      description: 'Waiting for food preparation',
      leftAnimation: 'waiting',
      rightAnimation: 'preparing'
    },
    {
      leftIcon: '🏠',
      rightIcon: '📦',
      description: 'Food being packaged',
      leftAnimation: 'waiting',
      rightAnimation: 'packaging'
    },
    {
      leftIcon: '🏠',
      rightIcon: '🛵',
      description: 'Delivery on the way',
      leftAnimation: 'waiting',
      rightAnimation: 'delivery'
    },
    {
      leftIcon: '🤝',
      rightIcon: '✅',
      description: 'Delivery completed',
      leftAnimation: 'meeting',
      rightAnimation: 'completed'
    },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentScene((prev) => (prev + 1) % storyScenes.length);
    }, 3000); // Change every 3 seconds for complete story

    return () => clearInterval(interval);
  }, []);

  const getAnimationStyle = (animationType: string) => {
    const animations = {
      phoneOrder: {
        animation: 'phoneOrder 2s ease-in-out infinite',
        '@keyframes phoneOrder': {
          '0%, 100%': { transform: 'scale(1) rotate(-5deg)' },
          '50%': { transform: 'scale(1.1) rotate(5deg)' },
        },
      },
      cooking: {
        animation: 'cooking 2s ease-in-out infinite',
        '@keyframes cooking': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.2) rotate(10deg)' },
        },
      },
      waiting: {
        animation: 'waiting 3s ease-in-out infinite',
        '@keyframes waiting': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(0.9)' },
        },
      },
      preparing: {
        animation: 'preparing 2s linear infinite',
        '@keyframes preparing': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
      },
      packaging: {
        animation: 'packaging 2s ease-in-out infinite',
        '@keyframes packaging': {
          '0%, 100%': { transform: 'scale(1)' },
          '25%': { transform: 'scale(1.1)' },
          '75%': { transform: 'scale(0.9)' },
        },
      },
      delivery: {
        animation: 'delivery 2s ease-in-out infinite',
        '@keyframes delivery': {
          '0%': { transform: 'translateX(-10px)' },
          '50%': { transform: 'translateX(10px)' },
          '100%': { transform: 'translateX(-10px)' },
        },
      },
      meeting: {
        animation: 'meeting 2s ease-in-out infinite',
        '@keyframes meeting': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.2)' },
        },
      },
      completed: {
        animation: 'completed 2s ease-in-out infinite',
        '@keyframes completed': {
          '0%, 100%': { transform: 'scale(1) rotate(0deg)' },
          '50%': { transform: 'scale(1.3) rotate(360deg)' },
        },
      },
    };
    return animations[animationType as keyof typeof animations] || {};
  };

  const currentStory = storyScenes[currentScene];

  return (
    <Box sx={{ textAlign: 'center', py: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        {/* Left Side - Customer */}
        <Box
          sx={{
            fontSize: '2rem',
            ...getAnimationStyle(currentStory.leftAnimation),
            transition: 'all 0.5s ease-in-out',
          }}
        >
          {currentStory.leftIcon}
        </Box>

        {/* Center - Arrow */}
        <Box sx={{
          fontSize: '1.5rem',
          color: '#FFFF00',
          animation: 'arrow 2s ease-in-out infinite',
          '@keyframes arrow': {
            '0%, 100%': { transform: 'translateX(0)' },
            '50%': { transform: 'translateX(5px)' },
          },
        }}>
          {currentScene >= 3 ? '⬅️' : '➡️'}
        </Box>

        {/* Right Side - Restaurant/Delivery */}
        <Box
          sx={{
            fontSize: '2rem',
            ...getAnimationStyle(currentStory.rightAnimation),
            transition: 'all 0.5s ease-in-out',
          }}
        >
          {currentStory.rightIcon}
        </Box>
      </Box>

      {/* Story Progress Dots */}
      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5, mt: 1 }}>
        {storyScenes.map((_, index) => (
          <Box
            key={index}
            sx={{
              width: 6,
              height: 6,
              borderRadius: '50%',
              backgroundColor: index === currentScene ? '#FFFF00' : '#666666',
              transition: 'all 0.3s ease',
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  permission?: string;
  badge?: string;
  color?: string;
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <Dashboard />,
    path: '/dashboard',
  },
  {
    id: 'pos',
    label: 'Point of Sale',
    icon: <PointOfSale />,
    path: '/pos',
    permission: 'pos',
    badge: 'HOT',
    color: 'error',
  },
  {
    id: 'menu',
    label: 'Menu Management',
    icon: <Restaurant />,
    path: '/menu',
    permission: 'menu',
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: <Inventory />,
    path: '/inventory',
    permission: 'inventory',
    badge: '5',
    color: 'warning',
  },
  {
    id: 'orders',
    label: 'Orders',
    icon: <ShoppingCart />,
    path: '/orders',
    permission: 'orders',
    badge: '12',
    color: 'info',
  },
  {
    id: 'reports',
    label: 'Reports & Analytics',
    icon: <Assessment />,
    path: '/reports',
    permission: 'reports',
  },
  {
    id: 'hr',
    label: 'Human Resources',
    icon: <People />,
    path: '/hr',
    permission: 'hr',
  },
  {
    id: 'finance',
    label: 'Finance',
    icon: <AccountBalance />,
    path: '/finance',
    permission: 'finance',
  },
  {
    id: 'crm',
    label: 'CRM & Loyalty',
    icon: <Support />,
    path: '/crm',
    permission: 'crm',
  },
];

const bottomMenuItems: MenuItem[] = [
  {
    id: 'settings',
    label: 'Settings',
    icon: <Settings />,
    path: '/settings',
  },
];

const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, hasPermission } = useAuth();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const renderMenuItem = (item: MenuItem) => {
    const isSelected = location.pathname === item.path;
    const hasAccess = !item.permission || hasPermission(item.permission);

    if (!hasAccess) return null;

    return (
      <ListItem key={item.id} disablePadding>
        <ListItemButton
          selected={isSelected}
          onClick={() => handleNavigation(item.path)}
          sx={{
            borderRadius: 0,
            mx: 1,
            mb: 0.5,
            border: '2px solid transparent',
            transition: 'all 0.1s ease',
            '&.Mui-selected': {
              backgroundColor: '#FFFF00', // Yellow background
              color: '#000000', // Black text
              border: '2px solid #FFFFFF',
              boxShadow: '3px 3px 0px #FFFFFF',
              transform: 'translate(-1px, -1px)',
              '&:hover': {
                backgroundColor: '#FFFF66',
                transform: 'translate(-2px, -2px)',
                boxShadow: '4px 4px 0px #FFFFFF',
              },
              '& .MuiListItemIcon-root': {
                color: '#000000', // Black icons on yellow
              },
              '& .MuiListItemText-primary': {
                color: '#000000', // Black text on yellow
                fontWeight: 800,
                textTransform: 'uppercase',
              },
            },
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 0, 0.2)',
              border: '2px solid #FFFF00',
            },
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 40,
              color: isSelected ? 'inherit' : 'text.secondary',
            }}
          >
            {item.icon}
          </ListItemIcon>
          <ListItemText
            primary={item.label}
            primaryTypographyProps={{
              fontSize: '0.875rem',
              fontWeight: isSelected ? 600 : 500,
            }}
          />
          {item.badge && (
            <Chip
              label={item.badge}
              size="small"
              color={item.color as any}
              sx={{
                height: 20,
                fontSize: '0.75rem',
                fontWeight: 600,
              }}
            />
          )}
        </ListItemButton>
      </ListItem>
    );
  };

  return (
    <SidebarContent>
      <SidebarHeader>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 2 }}>
          <DeliveryStoryAnimation />
          <Typography variant="h4" component="h1" fontWeight="bold" sx={{
            mt: 2,
            animation: 'textGlow 3s ease-in-out infinite alternate',
            '@keyframes textGlow': {
              '0%': {
                textShadow: '2px 2px 0px #000000, 0 0 10px #FFFF00',
              },
              '100%': {
                textShadow: '3px 3px 0px #000000, 0 0 20px #FFFF00, 0 0 30px #FFFF00',
              },
            },
          }}>
            ROS
          </Typography>
        </Box>
      </SidebarHeader>

      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>
        <List>
          {menuItems.map(renderMenuItem)}
        </List>

        <Box sx={{ px: 2, py: 1 }}>
          <Divider />
        </Box>

        <List>
          {bottomMenuItems.map(renderMenuItem)}
        </List>

        {/* Quick Stats */}
        <Box sx={{
          p: 2,
          mt: 'auto',
          border: '2px solid #FFFF00',
          borderLeft: 'none',
          borderRight: 'none',
          borderBottom: 'none',
          backgroundColor: 'rgba(255, 255, 0, 0.1)'
        }}>
          <Typography variant="subtitle2" sx={{ color: '#FFFF00', fontWeight: 800 }} gutterBottom>
            TODAY'S OVERVIEW
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="caption" sx={{ color: '#FFFFFF', fontWeight: 600 }}>
                Sales
              </Typography>
              <Typography variant="caption" fontWeight={800} sx={{ color: '#00FF00' }}>
                $2,450
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="caption" sx={{ color: '#FFFFFF', fontWeight: 600 }}>
                Orders
              </Typography>
              <Typography variant="caption" fontWeight={800} sx={{ color: '#00FFFF' }}>
                47
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="caption" sx={{ color: '#FFFFFF', fontWeight: 600 }}>
                Customers
              </Typography>
              <Typography variant="caption" fontWeight={800} sx={{ color: '#FFFF00' }}>
                32
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </SidebarContent>
  );
};

export default Sidebar;
