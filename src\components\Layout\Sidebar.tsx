import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Chip,
} from '@mui/material';
import {
  Dashboard,
  PointOfSale,
  Restaurant,
  Inventory,
  ShoppingCart,
  Assessment,
  People,
  AccountBalance,
  CustomerService,
  Settings,
  TrendingUp,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const SidebarHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3, 2),
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  textAlign: 'center',
}));

const SidebarContent = styled(Box)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  background: `linear-gradient(180deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
}));

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  permission?: string;
  badge?: string;
  color?: string;
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <Dashboard />,
    path: '/dashboard',
  },
  {
    id: 'pos',
    label: 'Point of Sale',
    icon: <PointOfSale />,
    path: '/pos',
    permission: 'pos',
    badge: 'HOT',
    color: 'error',
  },
  {
    id: 'menu',
    label: 'Menu Management',
    icon: <Restaurant />,
    path: '/menu',
    permission: 'menu',
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: <Inventory />,
    path: '/inventory',
    permission: 'inventory',
    badge: '5',
    color: 'warning',
  },
  {
    id: 'orders',
    label: 'Orders',
    icon: <ShoppingCart />,
    path: '/orders',
    permission: 'orders',
    badge: '12',
    color: 'info',
  },
  {
    id: 'reports',
    label: 'Reports & Analytics',
    icon: <Assessment />,
    path: '/reports',
    permission: 'reports',
  },
  {
    id: 'hr',
    label: 'Human Resources',
    icon: <People />,
    path: '/hr',
    permission: 'hr',
  },
  {
    id: 'finance',
    label: 'Finance',
    icon: <AccountBalance />,
    path: '/finance',
    permission: 'finance',
  },
  {
    id: 'crm',
    label: 'CRM & Loyalty',
    icon: <CustomerService />,
    path: '/crm',
    permission: 'crm',
  },
];

const bottomMenuItems: MenuItem[] = [
  {
    id: 'settings',
    label: 'Settings',
    icon: <Settings />,
    path: '/settings',
  },
];

const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, hasPermission } = useAuth();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const renderMenuItem = (item: MenuItem) => {
    const isSelected = location.pathname === item.path;
    const hasAccess = !item.permission || hasPermission(item.permission);

    if (!hasAccess) return null;

    return (
      <ListItem key={item.id} disablePadding>
        <ListItemButton
          selected={isSelected}
          onClick={() => handleNavigation(item.path)}
          sx={{
            borderRadius: 2,
            mx: 1,
            mb: 0.5,
            '&.Mui-selected': {
              backgroundColor: 'primary.main',
              color: 'primary.contrastText',
              '&:hover': {
                backgroundColor: 'primary.dark',
              },
              '& .MuiListItemIcon-root': {
                color: 'primary.contrastText',
              },
            },
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 40,
              color: isSelected ? 'inherit' : 'text.secondary',
            }}
          >
            {item.icon}
          </ListItemIcon>
          <ListItemText
            primary={item.label}
            primaryTypographyProps={{
              fontSize: '0.875rem',
              fontWeight: isSelected ? 600 : 500,
            }}
          />
          {item.badge && (
            <Chip
              label={item.badge}
              size="small"
              color={item.color as any}
              sx={{
                height: 20,
                fontSize: '0.75rem',
                fontWeight: 600,
              }}
            />
          )}
        </ListItemButton>
      </ListItem>
    );
  };

  return (
    <SidebarContent>
      <SidebarHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
          <Restaurant sx={{ fontSize: 32, mr: 1 }} />
          <Typography variant="h5" component="h1" fontWeight="bold">
            ROS
          </Typography>
        </Box>
        <Typography variant="caption" sx={{ opacity: 0.9 }}>
          Restaurant Operating System
        </Typography>
        <Box sx={{ mt: 2, p: 1, bgcolor: 'rgba(255,255,255,0.1)', borderRadius: 1 }}>
          <Typography variant="body2" fontWeight={500}>
            {user?.firstName} {user?.lastName}
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.8 }}>
            {user?.role?.toUpperCase()}
          </Typography>
        </Box>
      </SidebarHeader>

      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>
        <List>
          {menuItems.map(renderMenuItem)}
        </List>

        <Box sx={{ px: 2, py: 1 }}>
          <Divider />
        </Box>

        <List>
          {bottomMenuItems.map(renderMenuItem)}
        </List>

        {/* Quick Stats */}
        <Box sx={{ p: 2, mt: 'auto' }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Today's Overview
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                Sales
              </Typography>
              <Typography variant="caption" fontWeight={600} color="success.main">
                $2,450
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                Orders
              </Typography>
              <Typography variant="caption" fontWeight={600} color="info.main">
                47
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                Customers
              </Typography>
              <Typography variant="caption" fontWeight={600} color="secondary.main">
                32
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </SidebarContent>
  );
};

export default Sidebar;
