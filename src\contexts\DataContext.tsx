import React, { createContext, useContext, useState, ReactNode } from 'react';

// Types
export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  isAvailable: boolean;
  preparationTime: number;
  ingredients: string[];
  allergens: string[];
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

export interface Category {
  id: string;
  name: string;
  description: string;
  image?: string;
  sortOrder: number;
  isActive: boolean;
}

export interface Order {
  id: string;
  orderNumber: string;
  tableNumber?: string;
  customerName?: string;
  customerPhone?: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  status: 'pending' | 'preparing' | 'ready' | 'served' | 'cancelled';
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  createdAt: Date;
  updatedAt: Date;
  notes?: string;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  menuItem: MenuItem;
  quantity: number;
  price: number;
  modifiers: string[];
  notes?: string;
}

export interface InventoryItem {
  id: string;
  name: string;
  sku: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  costPrice: number;
  supplier: string;
  lastRestocked: Date;
  expiryDate?: Date;
}

interface DataContextType {
  // Menu data
  menuItems: MenuItem[];
  categories: Category[];
  
  // Orders data
  orders: Order[];
  currentOrder: Order | null;
  
  // Inventory data
  inventory: InventoryItem[];
  
  // Actions
  addMenuItem: (item: Omit<MenuItem, 'id'>) => void;
  updateMenuItem: (id: string, item: Partial<MenuItem>) => void;
  deleteMenuItem: (id: string) => void;
  
  addOrder: (order: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>) => void;
  updateOrder: (id: string, order: Partial<Order>) => void;
  setCurrentOrder: (order: Order | null) => void;
  
  updateInventoryItem: (id: string, item: Partial<InventoryItem>) => void;
  
  // Loading states
  isLoading: boolean;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

interface DataProviderProps {
  children: ReactNode;
}

// Mock data
const mockCategories: Category[] = [
  { id: '1', name: 'Appetizers', description: 'Start your meal right', sortOrder: 1, isActive: true },
  { id: '2', name: 'Main Courses', description: 'Hearty main dishes', sortOrder: 2, isActive: true },
  { id: '3', name: 'Desserts', description: 'Sweet endings', sortOrder: 3, isActive: true },
  { id: '4', name: 'Beverages', description: 'Refreshing drinks', sortOrder: 4, isActive: true },
];

const mockMenuItems: MenuItem[] = [
  {
    id: '1',
    name: 'Caesar Salad',
    description: 'Fresh romaine lettuce with parmesan cheese and croutons',
    price: 12.99,
    category: '1',
    isAvailable: true,
    preparationTime: 10,
    ingredients: ['romaine lettuce', 'parmesan cheese', 'croutons', 'caesar dressing'],
    allergens: ['dairy', 'gluten'],
  },
  {
    id: '2',
    name: 'Grilled Salmon',
    description: 'Atlantic salmon with herbs and lemon',
    price: 24.99,
    category: '2',
    isAvailable: true,
    preparationTime: 20,
    ingredients: ['salmon', 'herbs', 'lemon', 'olive oil'],
    allergens: ['fish'],
  },
  {
    id: '3',
    name: 'Chocolate Cake',
    description: 'Rich chocolate cake with vanilla ice cream',
    price: 8.99,
    category: '3',
    isAvailable: true,
    preparationTime: 5,
    ingredients: ['chocolate', 'flour', 'eggs', 'vanilla ice cream'],
    allergens: ['dairy', 'eggs', 'gluten'],
  },
];

const mockInventory: InventoryItem[] = [
  {
    id: '1',
    name: 'Romaine Lettuce',
    sku: 'VEG-001',
    category: 'Vegetables',
    currentStock: 50,
    minStock: 10,
    maxStock: 100,
    unit: 'heads',
    costPrice: 2.50,
    supplier: 'Fresh Farms Co.',
    lastRestocked: new Date('2024-01-15'),
    expiryDate: new Date('2024-01-25'),
  },
  {
    id: '2',
    name: 'Atlantic Salmon',
    sku: 'FISH-001',
    category: 'Seafood',
    currentStock: 20,
    minStock: 5,
    maxStock: 50,
    unit: 'lbs',
    costPrice: 15.00,
    supplier: 'Ocean Fresh',
    lastRestocked: new Date('2024-01-16'),
    expiryDate: new Date('2024-01-20'),
  },
];

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  const [menuItems, setMenuItems] = useState<MenuItem[]>(mockMenuItems);
  const [categories, setCategories] = useState<Category[]>(mockCategories);
  const [orders, setOrders] = useState<Order[]>([]);
  const [currentOrder, setCurrentOrder] = useState<Order | null>(null);
  const [inventory, setInventory] = useState<InventoryItem[]>(mockInventory);
  const [isLoading, setIsLoading] = useState(false);

  // Menu actions
  const addMenuItem = (item: Omit<MenuItem, 'id'>) => {
    const newItem: MenuItem = {
      ...item,
      id: Date.now().toString(),
    };
    setMenuItems(prev => [...prev, newItem]);
  };

  const updateMenuItem = (id: string, item: Partial<MenuItem>) => {
    setMenuItems(prev => prev.map(menuItem => 
      menuItem.id === id ? { ...menuItem, ...item } : menuItem
    ));
  };

  const deleteMenuItem = (id: string) => {
    setMenuItems(prev => prev.filter(item => item.id !== id));
  };

  // Order actions
  const addOrder = (orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>) => {
    const newOrder: Order = {
      ...orderData,
      id: Date.now().toString(),
      orderNumber: `ORD-${Date.now().toString().slice(-6)}`,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setOrders(prev => [newOrder, ...prev]);
    return newOrder;
  };

  const updateOrder = (id: string, orderData: Partial<Order>) => {
    setOrders(prev => prev.map(order => 
      order.id === id ? { ...order, ...orderData, updatedAt: new Date() } : order
    ));
  };

  // Inventory actions
  const updateInventoryItem = (id: string, itemData: Partial<InventoryItem>) => {
    setInventory(prev => prev.map(item => 
      item.id === id ? { ...item, ...itemData } : item
    ));
  };

  const value: DataContextType = {
    menuItems,
    categories,
    orders,
    currentOrder,
    inventory,
    addMenuItem,
    updateMenuItem,
    deleteMenuItem,
    addOrder,
    updateOrder,
    setCurrentOrder,
    updateInventoryItem,
    isLoading,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
