import { createTheme } from '@mui/material/styles';

// Premium Restaurant Theme Colors
const colors = {
  primary: {
    main: '#2C1810', // Rich Dark Brown
    light: '#4A2C1A',
    dark: '#1A0F08',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#D4AF37', // Elegant Gold
    light: '#E6C659',
    dark: '#B8941F',
    contrastText: '#000000',
  },
  accent: {
    main: '#8B4513', // Saddle Brown
    light: '#A0522D',
    dark: '#654321',
  },
  success: {
    main: '#2E7D32',
    light: '#4CAF50',
    dark: '#1B5E20',
  },
  warning: {
    main: '#F57C00',
    light: '#FF9800',
    dark: '#E65100',
  },
  error: {
    main: '#D32F2F',
    light: '#F44336',
    dark: '#C62828',
  },
  info: {
    main: '#1976D2',
    light: '#2196F3',
    dark: '#0D47A1',
  },
  background: {
    default: '#FAFAFA',
    paper: '#FFFFFF',
    dark: '#F5F5F5',
  },
  text: {
    primary: '#2C1810',
    secondary: '#5D4037',
    disabled: '#9E9E9E',
  },
  divider: '#E0E0E0',
};

export const theme = createTheme({
  palette: {
    mode: 'light',
    primary: colors.primary,
    secondary: colors.secondary,
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    info: colors.info,
    background: colors.background,
    text: colors.text,
    divider: colors.divider,
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      color: colors.primary.main,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      color: colors.primary.main,
      letterSpacing: '-0.01em',
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      color: colors.primary.main,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
      color: colors.primary.main,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      color: colors.primary.main,
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 500,
      color: colors.primary.main,
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 500,
      color: colors.text.secondary,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      color: colors.text.secondary,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      color: colors.text.primary,
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      color: colors.text.secondary,
      lineHeight: 1.5,
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 600,
      textTransform: 'none',
      letterSpacing: '0.02em',
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      color: colors.text.disabled,
    },
  },
  shape: {
    borderRadius: 12,
  },
  spacing: 8,
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          padding: '10px 24px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(44, 24, 16, 0.15)',
          },
        },
        contained: {
          background: `linear-gradient(135deg, ${colors.primary.main} 0%, ${colors.primary.dark} 100%)`,
          '&:hover': {
            background: `linear-gradient(135deg, ${colors.primary.dark} 0%, ${colors.primary.main} 100%)`,
          },
        },
        outlined: {
          borderColor: colors.primary.main,
          color: colors.primary.main,
          '&:hover': {
            backgroundColor: `${colors.primary.main}08`,
            borderColor: colors.primary.dark,
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 4px 20px rgba(44, 24, 16, 0.08)',
          border: `1px solid ${colors.divider}`,
          '&:hover': {
            boxShadow: '0 8px 32px rgba(44, 24, 16, 0.12)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 12px rgba(44, 24, 16, 0.06)',
        },
        elevation1: {
          boxShadow: '0 2px 8px rgba(44, 24, 16, 0.08)',
        },
        elevation2: {
          boxShadow: '0 4px 16px rgba(44, 24, 16, 0.10)',
        },
        elevation3: {
          boxShadow: '0 8px 24px rgba(44, 24, 16, 0.12)',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: colors.primary.light,
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: colors.primary.main,
              borderWidth: 2,
            },
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          background: `linear-gradient(135deg, ${colors.primary.main} 0%, ${colors.primary.dark} 100%)`,
          boxShadow: '0 4px 20px rgba(44, 24, 16, 0.15)',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          background: `linear-gradient(180deg, ${colors.background.paper} 0%, ${colors.background.dark} 100%)`,
          borderRight: `1px solid ${colors.divider}`,
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          margin: '4px 8px',
          '&:hover': {
            backgroundColor: `${colors.primary.main}08`,
          },
          '&.Mui-selected': {
            backgroundColor: `${colors.primary.main}12`,
            color: colors.primary.main,
            '&:hover': {
              backgroundColor: `${colors.primary.main}16`,
            },
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          fontWeight: 500,
        },
        filled: {
          backgroundColor: colors.secondary.main,
          color: colors.secondary.contrastText,
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.dark,
          '& .MuiTableCell-head': {
            fontWeight: 600,
            color: colors.text.primary,
          },
        },
      },
    },
  },
});

export default theme;
