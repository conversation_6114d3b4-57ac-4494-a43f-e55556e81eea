import { createTheme } from '@mui/material/styles';

// Neobrutalism Restaurant Theme Colors (60-30-10 rule)
const colors = {
  primary: {
    main: '#000000', // Black (60% - Primary)
    light: '#333333',
    dark: '#000000',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#FFFF00', // Bright Yellow (30% - Secondary)
    light: '#FFFF66',
    dark: '#CCCC00',
    contrastText: '#000000',
  },
  accent: {
    main: '#FF6B35', // Vibrant Orange (10% - Accent)
    light: '#FF8A65',
    dark: '#E64A19',
  },
  success: {
    main: '#00FF00',
    light: '#66FF66',
    dark: '#00CC00',
  },
  warning: {
    main: '#FFFF00',
    light: '#FFFF66',
    dark: '#CCCC00',
  },
  error: {
    main: '#FF0000',
    light: '#FF6666',
    dark: '#CC0000',
  },
  info: {
    main: '#00FFFF',
    light: '#66FFFF',
    dark: '#00CCCC',
  },
  background: {
    default: '#000000', // Black Background (60%)
    paper: '#000000',
    dark: '#111111',
  },
  text: {
    primary: '#FFFFFF', // White text on black background
    secondary: '#CCCCCC',
    disabled: '#888888',
  },
  divider: '#000000',
};

export const theme = createTheme({
  palette: {
    mode: 'light',
    primary: colors.primary,
    secondary: colors.secondary,
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    info: colors.info,
    background: colors.background,
    text: colors.text,
    divider: colors.divider,
  },
  typography: {
    fontFamily: '"Space Grotesk", "Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 900,
      color: colors.primary.main,
      letterSpacing: '-0.02em',
      textShadow: '3px 3px 0px #FFFF00',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 800,
      color: colors.primary.main,
      letterSpacing: '-0.01em',
      textShadow: '2px 2px 0px #FFFF00',
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 800,
      color: colors.primary.main,
      textShadow: '2px 2px 0px #FFFF00',
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 700,
      color: colors.primary.main,
      textShadow: '1px 1px 0px #FFFF00',
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 700,
      color: colors.primary.main,
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 700,
      color: colors.primary.main,
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 500,
      color: colors.text.secondary,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      color: colors.text.secondary,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      color: colors.text.primary,
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      color: colors.text.secondary,
      lineHeight: 1.5,
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 600,
      textTransform: 'none',
      letterSpacing: '0.02em',
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      color: colors.text.disabled,
    },
  },
  shape: {
    borderRadius: 0, // Sharp corners for neobrutalism
  },
  spacing: 8,
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 0,
          padding: '12px 24px',
          fontWeight: 800,
          textTransform: 'uppercase',
          border: '3px solid #000000',
          boxShadow: '4px 4px 0px #000000',
          transition: 'all 0.1s ease',
          '&:hover': {
            transform: 'translate(-2px, -2px)',
            boxShadow: '6px 6px 0px #000000',
          },
          '&:active': {
            transform: 'translate(0px, 0px)',
            boxShadow: '2px 2px 0px #000000',
          },
        },
        contained: {
          backgroundColor: colors.secondary.main,
          color: colors.primary.main,
          '&:hover': {
            backgroundColor: colors.secondary.light,
          },
        },
        outlined: {
          backgroundColor: colors.background.default,
          borderColor: colors.primary.main,
          color: colors.primary.main,
          '&:hover': {
            backgroundColor: colors.secondary.main,
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 0,
          border: '3px solid #000000',
          boxShadow: '6px 6px 0px #000000',
          backgroundColor: colors.background.paper,
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translate(-2px, -2px)',
            boxShadow: '8px 8px 0px #000000',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 0,
          border: '2px solid #000000',
          boxShadow: '4px 4px 0px #000000',
        },
        elevation1: {
          boxShadow: '2px 2px 0px #000000',
        },
        elevation2: {
          boxShadow: '4px 4px 0px #000000',
        },
        elevation3: {
          boxShadow: '6px 6px 0px #000000',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiInputLabel-root': {
            color: '#FFFFFF',
            backgroundColor: '#000000',
            padding: '0 4px',
            '&.Mui-focused': {
              color: '#FFFF00',
            },
          },
          '& .MuiOutlinedInput-root': {
            borderRadius: 0,
            border: '2px solid #FFFFFF',
            boxShadow: '2px 2px 0px #000000',
            backgroundColor: '#000000',
            '& input': {
              color: '#FFFFFF',
            },
            '& textarea': {
              color: '#FFFFFF',
            },
            '&:hover': {
              transform: 'translate(-1px, -1px)',
              boxShadow: '3px 3px 0px #000000',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#FFFF00',
              borderWidth: 2,
            },
            '&.Mui-focused': {
              transform: 'translate(-1px, -1px)',
              boxShadow: '3px 3px 0px #000000',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#FFFF00',
              borderWidth: 3,
            },
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: '#FFFFFF',
            },
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: colors.secondary.main,
          color: colors.primary.main,
          border: '3px solid #000000',
          borderRadius: 0,
          boxShadow: '4px 4px 0px #000000',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#000000', // Black background (60%)
          border: '3px solid #FFFF00',
          borderLeft: 'none',
          borderRadius: 0,
          boxShadow: '4px 0px 0px #FFFF00',
          color: '#FFFFFF',
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          margin: '4px 8px',
          '&:hover': {
            backgroundColor: `${colors.primary.main}08`,
          },
          '&.Mui-selected': {
            backgroundColor: `${colors.primary.main}12`,
            color: colors.primary.main,
            '&:hover': {
              backgroundColor: `${colors.primary.main}16`,
            },
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          fontWeight: 500,
        },
        filled: {
          backgroundColor: colors.secondary.main,
          color: colors.secondary.contrastText,
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.dark,
          '& .MuiTableCell-head': {
            fontWeight: 600,
            color: colors.text.primary,
          },
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          '& .MuiTabs-indicator': {
            backgroundColor: '#FFFF00',
            height: 3,
          },
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          color: '#CCCCCC',
          fontWeight: 600,
          textTransform: 'uppercase',
          '&.Mui-selected': {
            color: '#FFFF00',
          },
          '&:hover': {
            color: '#FFFFFF',
          },
        },
      },
    },
  },
});

export default theme;
