const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getPlatform: () => ipcRenderer.invoke('get-platform'),
  
  // Menu events
  onMenuNewOrder: (callback) => ipcRenderer.on('menu-new-order', callback),
  onMenuPrint: (callback) => ipcRenderer.on('menu-print', callback),
  onMenuNavigate: (callback) => ipcRenderer.on('menu-navigate', callback),
  onMenuAbout: (callback) => ipcRenderer.on('menu-about', callback),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // File operations
  saveFile: (data) => ipc<PERSON>enderer.invoke('save-file', data),
  loadFile: () => ipc<PERSON>enderer.invoke('load-file'),
  
  // Print operations
  print: (data) => ipc<PERSON>enderer.invoke('print', data),
  
  // Database operations (for future use)
  dbQuery: (query, params) => ipcRenderer.invoke('db-query', query, params),
  
  // System operations
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options)
});
