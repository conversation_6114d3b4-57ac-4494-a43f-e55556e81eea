import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  ShoppingCart,
  Restaurant,
  LocalShipping,
  Storefront,
  MoreVert,
  Visibility,
  Print,
  Edit,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useData } from '../contexts/DataContext';

const OrdersPage: React.FC = () => {
  const { orders } = useData();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, orderId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedOrder(orderId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedOrder(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'preparing': return 'info';
      case 'ready': return 'success';
      case 'served': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getOrderTypeIcon = (type: string) => {
    switch (type) {
      case 'dine-in': return <Restaurant />;
      case 'takeaway': return <Storefront />;
      case 'delivery': return <LocalShipping />;
      default: return <ShoppingCart />;
    }
  };

  // Mock orders for display
  const mockOrders = [
    {
      id: '1',
      orderNumber: 'ORD-001',
      customerName: 'John Doe',
      customerPhone: '+1234567890',
      tableNumber: '5',
      total: 45.99,
      status: 'preparing',
      orderType: 'dine-in',
      createdAt: new Date(),
      items: [
        { menuItem: { name: 'Caesar Salad' }, quantity: 2, price: 12.99 },
        { menuItem: { name: 'Grilled Salmon' }, quantity: 1, price: 24.99 }
      ]
    },
    {
      id: '2',
      orderNumber: 'ORD-002',
      customerName: 'Jane Smith',
      customerPhone: '+1234567891',
      total: 32.50,
      status: 'ready',
      orderType: 'takeaway',
      createdAt: new Date(Date.now() - 30 * 60 * 1000),
      items: [
        { menuItem: { name: 'Pasta Carbonara' }, quantity: 1, price: 18.99 },
        { menuItem: { name: 'Chocolate Cake' }, quantity: 1, price: 8.99 }
      ]
    }
  ];

  const allOrders = [...orders, ...mockOrders];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
            color: '#FFFF00',
            textShadow: '2px 2px 0px #000000',
            marginBottom: '24px'
          }}>
            ORDERS MANAGEMENT
          </Typography>
          <Typography variant="body1" sx={{ color: '#FFFFFF', fontSize: '1.1rem' }}>
            Track and manage all restaurant orders
          </Typography>
        </Box>
      </Box>

      {/* Order Status Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFA500',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFA500',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFA500',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFA500' }}>
                    5
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Pending Orders
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFA500', color: '#000000' }}>
                  <ShoppingCart />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FFFF',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FFFF',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FFFF',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FFFF' }}>
                    8
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Preparing
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FFFF', color: '#000000' }}>
                  <Restaurant />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FF00' }}>
                    3
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Ready
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FF00', color: '#000000' }}>
                  <LocalShipping />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFF00' }}>
                    47
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Today's Total
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFFF00', color: '#000000' }}>
                  <ShoppingCart />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Orders Table */}
      <Card sx={{
        border: '3px solid #FFFF00',
        backgroundColor: '#000000',
        borderRadius: 0,
        boxShadow: '4px 4px 0px #FFFF00',
      }}>
        <CardContent>
          <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
            Recent Orders
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Order #</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Items</TableCell>
                  <TableCell>Total</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Time</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {allOrders.map((order) => (
                  <TableRow key={order.id} hover>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {order.orderNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {order.customerName || 'Walk-in Customer'}
                        </Typography>
                        {order.tableNumber && (
                          <Typography variant="caption" color="text.secondary">
                            Table {order.tableNumber}
                          </Typography>
                        )}
                        {order.customerPhone && (
                          <Typography variant="caption" color="text.secondary" display="block">
                            {order.customerPhone}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: 'secondary.main', mr: 1, width: 24, height: 24 }}>
                          {getOrderTypeIcon(order.orderType)}
                        </Avatar>
                        <Typography variant="caption">
                          {order.orderType.charAt(0).toUpperCase() + order.orderType.slice(1)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {order.items?.length || 0} items
                      </Typography>
                      {order.items && order.items.length > 0 && (
                        <Typography variant="caption" color="text.secondary">
                          {order.items[0].menuItem.name}
                          {order.items.length > 1 && ` +${order.items.length - 1} more`}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        ${order.total.toFixed(2)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        color={getStatusColor(order.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption">
                        {format(new Date(order.createdAt), 'HH:mm')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, order.id)}
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <Visibility sx={{ mr: 1 }} fontSize="small" />
          View Details
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Edit sx={{ mr: 1 }} fontSize="small" />
          Edit Order
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Print sx={{ mr: 1 }} fontSize="small" />
          Print Receipt
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default OrdersPage;
