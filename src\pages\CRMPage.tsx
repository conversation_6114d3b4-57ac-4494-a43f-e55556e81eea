import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
} from '@mui/material';
import {
  People,
  PersonAdd,
  Star,
  LocalOffer,
  TrendingUp,
  Email,
  Phone,
} from '@mui/icons-material';

const CRMPage: React.FC = () => {
  const customers = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890',
      visits: 15,
      totalSpent: 450.75,
      loyaltyPoints: 225,
      tier: 'Gold',
      lastVisit: '2024-01-18',
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567891',
      visits: 8,
      totalSpent: 280.50,
      loyaltyPoints: 140,
      tier: 'Silver',
      lastVisit: '2024-01-20',
    },
    {
      id: '3',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567892',
      visits: 3,
      totalSpent: 95.25,
      loyaltyPoints: 48,
      tier: 'Bronze',
      lastVisit: '2024-01-19',
    },
  ];

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Gold': return 'warning';
      case 'Silver': return 'info';
      case 'Bronze': return 'success';
      default: return 'default';
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
            color: '#FFFF00',
            textShadow: '2px 2px 0px #000000',
            border: '3px solid #FFFF00',
            padding: '16px',
            backgroundColor: '#000000',
            marginBottom: '24px'
          }}>
            CRM & LOYALTY PROGRAM
          </Typography>
          <Typography variant="body1" sx={{ color: '#FFFFFF', fontSize: '1.1rem' }}>
            Manage customer relationships and loyalty rewards
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<PersonAdd />} size="large">
          Add Customer
        </Button>
      </Box>

      {/* CRM Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFFF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFF00' }}>
                    1,247
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Total Customers
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFFF00', color: '#000000' }}>
                  <People />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FF00',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FF00',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FF00' }}>
                    89
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    New This Month
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FF00', color: '#000000' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #FFA500',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFA500',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #FFA500',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFA500' }}>
                    4.8
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Avg Rating
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFA500', color: '#000000' }}>
                  <Star />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            border: '3px solid #00FFFF',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #00FFFF',
            '&:hover': {
              transform: 'translate(-2px, -2px)',
              boxShadow: '6px 6px 0px #00FFFF',
            }
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#00FFFF' }}>
                    12,450
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Points Redeemed
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FFFF', color: '#000000' }}>
                  <LocalOffer />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Loyalty Program Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
          }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                Loyalty Tiers
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Bronze (0-100 points)</Typography>
                  <Typography variant="body2">65%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={65} color="success" />
              </Box>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Silver (101-300 points)</Typography>
                  <Typography variant="body2">25%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={25} color="info" />
              </Box>
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Gold (301+ points)</Typography>
                  <Typography variant="body2">10%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={10} color="warning" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card sx={{
            border: '3px solid #FFFF00',
            backgroundColor: '#000000',
            borderRadius: 0,
            boxShadow: '4px 4px 0px #FFFF00',
          }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                Top Customers
              </Typography>
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Customer</TableCell>
                      <TableCell>Contact</TableCell>
                      <TableCell>Visits</TableCell>
                      <TableCell>Total Spent</TableCell>
                      <TableCell>Points</TableCell>
                      <TableCell>Tier</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {customers.map((customer) => (
                      <TableRow key={customer.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                              {customer.name.charAt(0)}
                            </Avatar>
                            <Typography variant="subtitle2">{customer.name}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                              <Email sx={{ fontSize: 16, mr: 0.5 }} />
                              {customer.email}
                            </Typography>
                            <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                              <Phone sx={{ fontSize: 16, mr: 0.5 }} />
                              {customer.phone}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{customer.visits}</TableCell>
                        <TableCell>${customer.totalSpent.toFixed(2)}</TableCell>
                        <TableCell>{customer.loyaltyPoints}</TableCell>
                        <TableCell>
                          <Chip
                            label={customer.tier}
                            color={getTierColor(customer.tier) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Button size="small" variant="outlined">
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CRMPage;
