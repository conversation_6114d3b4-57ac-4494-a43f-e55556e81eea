import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  LinearProgress,
  Chip,
  IconButton,
  Paper,
  Tabs,
  Tab,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AttachMoney,
  ShoppingCart,
  People,
  Restaurant,
  Inventory,
  Assessment,
  MoreVert,
  Psychology,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import AIDashboard from '../components/AI/AIDashboard';

const StatsCard = styled(Card)(({ theme }) => ({
  height: '100%',
  backgroundColor: '#000000',
  border: '3px solid #FFFF00',
  borderRadius: 0,
  boxShadow: '4px 4px 0px #FFFF00',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translate(-2px, -2px)',
    boxShadow: '6px 6px 0px #FFFF00',
  },
}));

const MetricCard = styled(Card)(({ theme }) => ({
  backgroundColor: '#FFFF00',
  color: '#000000',
  height: '100%',
  position: 'relative',
  overflow: 'hidden',
  border: '3px solid #000000',
  borderRadius: 0,
  boxShadow: '4px 4px 0px #000000',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translate(-2px, -2px)',
    boxShadow: '6px 6px 0px #000000',
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '100px',
    height: '100px',
    background: 'rgba(0, 0, 0, 0.1)',
    borderRadius: '50%',
    transform: 'translate(30px, -30px)',
  },
}));

// Mock data
const salesData = [
  { name: 'Mon', sales: 2400, orders: 24 },
  { name: 'Tue', sales: 1398, orders: 18 },
  { name: 'Wed', sales: 9800, orders: 45 },
  { name: 'Thu', sales: 3908, orders: 32 },
  { name: 'Fri', sales: 4800, orders: 38 },
  { name: 'Sat', sales: 3800, orders: 35 },
  { name: 'Sun', sales: 4300, orders: 41 },
];

const categoryData = [
  { name: 'Main Courses', value: 45, color: '#2C1810' },
  { name: 'Appetizers', value: 25, color: '#D4AF37' },
  { name: 'Desserts', value: 20, color: '#8B4513' },
  { name: 'Beverages', value: 10, color: '#A0522D' },
];

const topItems = [
  { name: 'Grilled Salmon', orders: 45, revenue: 1125 },
  { name: 'Caesar Salad', orders: 38, revenue: 494 },
  { name: 'Chocolate Cake', orders: 32, revenue: 288 },
  { name: 'Pasta Carbonara', orders: 28, revenue: 560 },
];

const Dashboard: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
        color: '#FFFF00',
        textShadow: '2px 2px 0px #000000',
        marginBottom: '24px'
      }}>
        DASHBOARD
      </Typography>
      <Typography variant="body1" sx={{ mb: 3, color: '#FFFFFF', fontSize: '1.1rem' }}>
        Welcome back! Here's what's happening at your restaurant today.
      </Typography>

      {/* Dashboard Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Overview" />
          <Tab label="AI Insights" icon={<Psychology />} iconPosition="start" />
        </Tabs>
      </Box>

      {tabValue === 0 && (
        <Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold">
                    $2,450
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#000000', fontWeight: 'bold' }}>
                    Today's Sales
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FF00', color: '#000000' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <TrendingUp sx={{ fontSize: 16, mr: 0.5, color: '#00FF00' }} />
                <Typography variant="caption" sx={{ color: '#00FF00', fontWeight: 'bold' }}>
                  +12% from yesterday
                </Typography>
              </Box>
            </CardContent>
          </MetricCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                    47
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Orders Today
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFFF00', color: '#000000' }}>
                  <ShoppingCart />
                </Avatar>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <TrendingUp sx={{ fontSize: 16, mr: 0.5, color: 'success.main' }} />
                <Typography variant="caption" color="success.main">
                  +8% from yesterday
                </Typography>
              </Box>
            </CardContent>
          </StatsCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                    32
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Customers
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#00FFFF', color: '#000000' }}>
                  <People />
                </Avatar>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <TrendingDown sx={{ fontSize: 16, mr: 0.5, color: 'error.main' }} />
                <Typography variant="caption" color="error.main">
                  -3% from yesterday
                </Typography>
              </Box>
            </CardContent>
          </StatsCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" component="div" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                    $52
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#CCCCCC' }}>
                    Avg Order Value
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: '#FFA500', color: '#000000' }}>
                  <Assessment />
                </Avatar>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <TrendingUp sx={{ fontSize: 16, mr: 0.5, color: 'success.main' }} />
                <Typography variant="caption" color="success.main">
                  +15% from yesterday
                </Typography>
              </Box>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <StatsCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6" component="h2" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                  Sales Overview
                </Typography>
                <IconButton size="small" sx={{ color: '#FFFF00' }}>
                  <MoreVert />
                </IconButton>
              </Box>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#333333" />
                  <XAxis dataKey="name" tick={{ fill: '#FFFFFF' }} />
                  <YAxis tick={{ fill: '#FFFFFF' }} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#000000',
                      border: '2px solid #FFFF00',
                      color: '#FFFFFF'
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="sales"
                    stroke="#FFFF00"
                    strokeWidth={4}
                    dot={{ fill: '#FFFF00', strokeWidth: 2, r: 8, stroke: '#000000' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </StatsCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <StatsCard>
            <CardContent>
              <Typography variant="h6" component="h2" fontWeight="bold" gutterBottom sx={{ color: '#FFFFFF' }}>
                Sales by Category
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#000000',
                      border: '2px solid #FFFF00',
                      color: '#FFFFFF'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
              <Box sx={{ mt: 2 }}>
                {categoryData.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        bgcolor: item.color,
                        borderRadius: '50%',
                        mr: 1,
                      }}
                    />
                    <Typography variant="caption" sx={{ flex: 1, color: '#FFFFFF' }}>
                      {item.name}
                    </Typography>
                    <Typography variant="caption" fontWeight="bold" sx={{ color: '#FFFF00' }}>
                      {item.value}%
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>

      {/* Top Items and Recent Activity */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <StatsCard>
            <CardContent>
              <Typography variant="h6" component="h2" fontWeight="bold" gutterBottom sx={{ color: '#FFFFFF' }}>
                Top Selling Items
              </Typography>
              {topItems.map((item, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'center', py: 2, borderBottom: index < topItems.length - 1 ? '1px solid' : 'none', borderColor: 'divider' }}>
                  <Avatar sx={{ bgcolor: '#FFFF00', color: '#000000', mr: 2 }}>
                    <Restaurant />
                  </Avatar>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="subtitle2" fontWeight="bold" sx={{ color: '#FFFFFF' }}>
                      {item.name}
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#CCCCCC' }}>
                      {item.orders} orders • ${item.revenue}
                    </Typography>
                  </Box>
                  <Chip
                    label={`#${index + 1}`}
                    size="small"
                    color={index === 0 ? 'primary' : 'default'}
                    variant={index === 0 ? 'filled' : 'outlined'}
                  />
                </Box>
              ))}
            </CardContent>
          </StatsCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <StatsCard>
            <CardContent>
              <Typography variant="h6" component="h2" fontWeight="bold" gutterBottom sx={{ color: '#FFFFFF' }}>
                Inventory Alerts
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" sx={{ color: '#FFFFFF' }}>Romaine Lettuce</Typography>
                  <Typography variant="body2" sx={{ color: '#FFA500' }}>Low Stock</Typography>
                </Box>
                <LinearProgress variant="determinate" value={20} color="warning" />
                <Typography variant="caption" sx={{ color: '#CCCCCC' }}>
                  10 units remaining
                </Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Atlantic Salmon</Typography>
                  <Typography variant="body2" color="error.main">Critical</Typography>
                </Box>
                <LinearProgress variant="determinate" value={5} color="error" />
                <Typography variant="caption" color="text.secondary">
                  2 lbs remaining
                </Typography>
              </Box>

              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Chicken Breast</Typography>
                  <Typography variant="body2" color="success.main">Good</Typography>
                </Box>
                <LinearProgress variant="determinate" value={75} color="success" />
                <Typography variant="caption" color="text.secondary">
                  45 lbs remaining
                </Typography>
              </Box>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>
        </Box>
      )}

      {tabValue === 1 && (
        <AIDashboard />
      )}
    </Box>
  );
};

export default Dashboard;
