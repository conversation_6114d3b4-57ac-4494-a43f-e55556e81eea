import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Button,
  TextField,
  Chip,
  Avatar,
  Divider,
  IconButton,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add,
  Remove,
  Delete,
  Payment,
  Print,
  TableRestaurant,
  Person,
  Phone,
  ShoppingCart,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { useData, MenuItem as MenuItemType, OrderItem } from '../contexts/DataContext';
import { getFoodImage } from '../utils/foodImages';

const POSContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  height: 'calc(100vh - 120px)',
  gap: theme.spacing(2),
}));

const MenuSection = styled(Card)(({ theme }) => ({
  flex: 2,
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
}));

const OrderSection = styled(Card)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  minWidth: 350,
  border: '3px solid #FFFF00',
  backgroundColor: '#000000',
  borderRadius: 0,
  boxShadow: '4px 4px 0px #FFFF00',
}));

const MenuGrid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  overflow: 'auto',
  flex: 1,
  maxHeight: '70vh',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: '#f1f1f1',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#FFFF00',
    borderRadius: '4px',
    border: '2px solid #000000',
  },
}));

const MenuItemCard = styled(Card)(({ theme }) => ({
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  border: `3px solid #000000`,
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  '&:hover': {
    transform: 'translate(-2px, -2px)',
    boxShadow: '6px 6px 0px #000000',
  },
}));

const OrderSummary = styled(Box)(({ theme }) => ({
  flex: 1,
  overflow: 'auto',
  padding: theme.spacing(2),
  maxHeight: '50vh',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    backgroundColor: '#f1f1f1',
    borderRadius: '4px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: '#FFFF00',
    borderRadius: '4px',
    border: '2px solid #000000',
  },
}));

const POSPage: React.FC = () => {
  const { menuItems, categories, addOrder } = useData();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [tableNumber, setTableNumber] = useState('');
  const [orderType, setOrderType] = useState<'dine-in' | 'takeaway' | 'delivery'>('dine-in');
  const [paymentDialog, setPaymentDialog] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('cash');

  const filteredMenuItems = selectedCategory === 'all' 
    ? menuItems 
    : menuItems.filter(item => item.category === selectedCategory);

  const addToOrder = (menuItem: MenuItemType) => {
    const existingItem = currentOrder.find(item => item.menuItemId === menuItem.id);
    
    if (existingItem) {
      setCurrentOrder(prev => prev.map(item => 
        item.menuItemId === menuItem.id 
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      const newOrderItem: OrderItem = {
        id: Date.now().toString(),
        menuItemId: menuItem.id,
        menuItem,
        quantity: 1,
        price: menuItem.price,
        modifiers: [],
      };
      setCurrentOrder(prev => [...prev, newOrderItem]);
    }
  };

  const updateQuantity = (itemId: string, change: number) => {
    setCurrentOrder(prev => prev.map(item => {
      if (item.id === itemId) {
        const newQuantity = Math.max(0, item.quantity + change);
        return newQuantity === 0 ? null : { ...item, quantity: newQuantity };
      }
      return item;
    }).filter(Boolean) as OrderItem[]);
  };

  const removeFromOrder = (itemId: string) => {
    setCurrentOrder(prev => prev.filter(item => item.id !== itemId));
  };

  const calculateTotal = () => {
    const subtotal = currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * 0.08; // 8% tax
    const total = subtotal + tax;
    return { subtotal, tax, total };
  };

  const handlePayment = () => {
    const { subtotal, tax, total } = calculateTotal();
    
    const order = {
      tableNumber: orderType === 'dine-in' ? tableNumber : undefined,
      customerName: customerName || undefined,
      customerPhone: customerPhone || undefined,
      items: currentOrder,
      subtotal,
      tax,
      discount: 0,
      total,
      status: 'pending' as const,
      orderType,
      notes: '',
    };

    addOrder(order);
    
    // Reset form
    setCurrentOrder([]);
    setCustomerName('');
    setCustomerPhone('');
    setTableNumber('');
    setPaymentDialog(false);
    
    alert('Order placed successfully!');
  };

  const { subtotal, tax, total } = calculateTotal();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{
        color: '#FFFF00',
        textShadow: '2px 2px 0px #000000',
        border: '3px solid #FFFF00',
        padding: '16px',
        backgroundColor: '#000000',
        marginBottom: '24px'
      }}>
        POINT OF SALE
      </Typography>
      
      <POSContainer>
        <MenuSection>
          <CardContent sx={{ pb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6" fontWeight="bold">
                Menu Items
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label="All"
                  onClick={() => setSelectedCategory('all')}
                  color={selectedCategory === 'all' ? 'primary' : 'default'}
                  variant={selectedCategory === 'all' ? 'filled' : 'outlined'}
                />
                {categories.map(category => (
                  <Chip
                    key={category.id}
                    label={category.name}
                    onClick={() => setSelectedCategory(category.id)}
                    color={selectedCategory === category.id ? 'primary' : 'default'}
                    variant={selectedCategory === category.id ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
            </Box>
          </CardContent>
          
          <MenuGrid>
            {filteredMenuItems.map(item => (
              <MenuItemCard key={item.id} onClick={() => addToOrder(item)}>
                <CardMedia
                  component="img"
                  height="180"
                  image={getFoodImage(item.name, item.category)}
                  alt={item.name}
                  sx={{
                    objectFit: 'cover',
                    width: '100%',
                    display: 'block',
                  }}
                />
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="h6" component="h3" gutterBottom noWrap fontWeight={800} sx={{ color: '#FFFFFF' }}>
                    {item.name.toUpperCase()}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2, height: 40, overflow: 'hidden', color: '#CCCCCC', flexGrow: 1 }}>
                    {item.description}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
                    <Typography variant="h6" fontWeight="bold" sx={{ color: '#FFFF00' }}>
                      ${item.price.toFixed(2)}
                    </Typography>
                    <Chip
                      label={item.isAvailable ? 'Available' : 'Out of Stock'}
                      sx={{
                        backgroundColor: item.isAvailable ? '#00FF00' : '#FF0000',
                        color: '#000000',
                        fontWeight: 'bold',
                        border: '2px solid #000000',
                      }}
                      size="small"
                    />
                  </Box>
                </CardContent>
              </MenuItemCard>
            ))}
          </MenuGrid>
        </MenuSection>

        <OrderSection>
          <CardContent sx={{ pb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6" fontWeight="bold">
                Current Order
              </Typography>
              <Badge badgeContent={currentOrder.length} color="primary">
                <ShoppingCart />
              </Badge>
            </Box>
            
            {/* Order Type Selection */}
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              {(['dine-in', 'takeaway', 'delivery'] as const).map(type => (
                <Chip
                  key={type}
                  label={type.charAt(0).toUpperCase() + type.slice(1)}
                  onClick={() => setOrderType(type)}
                  color={orderType === type ? 'primary' : 'default'}
                  variant={orderType === type ? 'filled' : 'outlined'}
                  size="small"
                />
              ))}
            </Box>

            {/* Customer Info */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 2 }}>
              {orderType === 'dine-in' && (
                <TextField
                  size="small"
                  label="Table Number"
                  value={tableNumber}
                  onChange={(e) => setTableNumber(e.target.value)}
                  fullWidth
                  variant="outlined"
                  sx={{
                    '& .MuiInputLabel-root': {
                      color: '#FFFFFF',
                      backgroundColor: '#000000',
                      padding: '0 4px',
                      '&.Mui-focused': {
                        color: '#FFFF00',
                      },
                    },
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: '#FFFFFF',
                      },
                      '&:hover fieldset': {
                        borderColor: '#FFFF00',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#FFFF00',
                      },
                      '& input': {
                        color: '#FFFFFF',
                      },
                    },
                  }}
                  InputProps={{
                    startAdornment: <TableRestaurant sx={{ mr: 1, color: '#FFFF00' }} />,
                  }}
                />
              )}
              <TextField
                size="small"
                label="Customer Name"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                fullWidth
                variant="outlined"
                sx={{
                  '& .MuiInputLabel-root': {
                    color: '#FFFFFF',
                    backgroundColor: '#000000',
                    padding: '0 4px',
                    '&.Mui-focused': {
                      color: '#FFFF00',
                    },
                  },
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      borderColor: '#FFFFFF',
                    },
                    '&:hover fieldset': {
                      borderColor: '#FFFF00',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#FFFF00',
                    },
                    '& input': {
                      color: '#FFFFFF',
                    },
                  },
                }}
                InputProps={{
                  startAdornment: <Person sx={{ mr: 1, color: '#FFFF00' }} />,
                }}
              />
            </Box>
          </CardContent>

          <OrderSummary>
            {currentOrder.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <ShoppingCart sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                <Typography variant="body1" color="text.secondary">
                  No items in order
                </Typography>
                <Typography variant="body2" color="text.disabled">
                  Select items from the menu to get started
                </Typography>
              </Box>
            ) : (
              currentOrder.map(item => (
                <Card key={item.id} sx={{ mb: 1, border: '1px solid', borderColor: 'divider' }}>
                  <CardContent sx={{ py: 1.5, '&:last-child': { pb: 1.5 } }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {item.menuItem.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          ${item.price.toFixed(2)} each
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <IconButton size="small" onClick={() => updateQuantity(item.id, -1)}>
                          <Remove />
                        </IconButton>
                        <Typography variant="body1" fontWeight="bold" sx={{ minWidth: 20, textAlign: 'center' }}>
                          {item.quantity}
                        </Typography>
                        <IconButton size="small" onClick={() => updateQuantity(item.id, 1)}>
                          <Add />
                        </IconButton>
                        <IconButton size="small" color="error" onClick={() => removeFromOrder(item.id)}>
                          <Delete />
                        </IconButton>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Total: ${(item.price * item.quantity).toFixed(2)}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              ))
            )}
          </OrderSummary>

          {currentOrder.length > 0 && (
            <CardContent sx={{ pt: 1 }}>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Subtotal:</Typography>
                  <Typography variant="body2">${subtotal.toFixed(2)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Tax (8%):</Typography>
                  <Typography variant="body2">${tax.toFixed(2)}</Typography>
                </Box>
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" fontWeight="bold">Total:</Typography>
                  <Typography variant="h6" fontWeight="bold" color="primary">
                    ${total.toFixed(2)}
                  </Typography>
                </Box>
              </Box>
              
              <Button
                fullWidth
                variant="contained"
                size="large"
                startIcon={<Payment />}
                onClick={() => setPaymentDialog(true)}
                sx={{ mb: 1 }}
              >
                Process Payment
              </Button>
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Print />}
                onClick={() => window.print()}
              >
                Print Receipt
              </Button>
            </CardContent>
          )}
        </OrderSection>
      </POSContainer>

      {/* Payment Dialog */}
      <Dialog open={paymentDialog} onClose={() => setPaymentDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Process Payment</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Order Total: ${total.toFixed(2)}
            </Typography>
          </Box>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Payment Method</InputLabel>
            <Select
              value={paymentMethod}
              onChange={(e) => setPaymentMethod(e.target.value)}
              label="Payment Method"
            >
              <MenuItem value="cash">Cash</MenuItem>
              <MenuItem value="card">Credit/Debit Card</MenuItem>
              <MenuItem value="digital">Digital Wallet</MenuItem>
            </Select>
          </FormControl>

          {orderType !== 'dine-in' && (
            <TextField
              fullWidth
              label="Phone Number"
              value={customerPhone}
              onChange={(e) => setCustomerPhone(e.target.value)}
              sx={{ mb: 2 }}
              InputProps={{
                startAdornment: <Phone sx={{ mr: 1, color: 'action.active' }} />,
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPaymentDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handlePayment}>
            Complete Payment
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default POSPage;
