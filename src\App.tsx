import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { useAuth } from './contexts/AuthContext';
import LoginPage from './pages/LoginPage';
import Dashboard from './pages/Dashboard';
import POSPage from './pages/POSPage';
import MenuPage from './pages/MenuPage';
import InventoryPage from './pages/InventoryPage';
import OrdersPage from './pages/OrdersPage';
import ReportsPage from './pages/ReportsPage';
import HRPage from './pages/HRPage';
import FinancePage from './pages/FinancePage';
import CRMPage from './pages/CRMPage';
import SettingsPage from './pages/SettingsPage';
import Layout from './components/Layout/Layout';
import LoadingScreen from './components/Common/LoadingScreen';

const App: React.FC = () => {
  const { isAuthenticated, isLoading, user } = useAuth();

  // Handle Electron menu events
  useEffect(() => {
    try {
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        const electronAPI = (window as any).electronAPI;

        const handleMenuNavigate = (_event: any, route: string) => {
          window.location.hash = `#/${route}`;
        };

        const handleMenuNewOrder = () => {
          window.location.hash = '#/pos';
        };

        const handleMenuPrint = () => {
          window.print();
        };

        const handleMenuAbout = () => {
          alert('Restaurant Operating System v1.0.0\nDeveloped with ❤️ for restaurants');
        };

        // Register event listeners safely
        if (electronAPI.onMenuNavigate) electronAPI.onMenuNavigate(handleMenuNavigate);
        if (electronAPI.onMenuNewOrder) electronAPI.onMenuNewOrder(handleMenuNewOrder);
        if (electronAPI.onMenuPrint) electronAPI.onMenuPrint(handleMenuPrint);
        if (electronAPI.onMenuAbout) electronAPI.onMenuAbout(handleMenuAbout);

        // Cleanup
        return () => {
          try {
            if (electronAPI.removeAllListeners) {
              electronAPI.removeAllListeners('menu-navigate');
              electronAPI.removeAllListeners('menu-new-order');
              electronAPI.removeAllListeners('menu-print');
              electronAPI.removeAllListeners('menu-about');
            }
          } catch (error) {
            console.log('Cleanup error:', error);
          }
        };
      }
    } catch (error) {
      console.log('Electron API setup error:', error);
    }
  }, []);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/pos" element={<POSPage />} />
          <Route path="/menu" element={<MenuPage />} />
          <Route path="/inventory" element={<InventoryPage />} />
          <Route path="/orders" element={<OrdersPage />} />
          <Route path="/reports" element={<ReportsPage />} />
          <Route path="/hr" element={<HRPage />} />
          <Route path="/finance" element={<FinancePage />} />
          <Route path="/crm" element={<CRMPage />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Layout>
    </Box>
  );
};

export default App;
